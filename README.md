# 个人健康助理 (Health Assistant Agent)

基于React + TypeScript + Vite构建的现代化移动端健康管理H5应用，集成Dify AI智能体，提供专业的健康咨询和报告解读服务。

## ✨ 功能特性

### 🩺 报告解读智能体
- **拍照解读**: 实时拍摄体检报告，AI智能解读分析
- **照片解读**: 上传报告图片，获得专业医学解读
- 支持多种报告格式：血常规、生化、影像等
- 详细的健康指标分析和建议

### 👨‍⚕️ 全科医生智能体  
- **AI健康咨询**: 24小时在线智能医生，多轮对话咨询
- **症状自评**: 结构化症状记录，智能风险评估
- **趋势分析**: 健康数据追踪，可视化趋势分析
- 个性化健康建议和就医指导

### 📱 用户体验
- 响应式设计，完美适配移动端
- PWA支持，可安装到手机桌面
- 流畅的动画效果，优秀的交互体验
- 本地数据存储，保护隐私安全

## 🚀 技术栈

- **前端框架**: React 18 + TypeScript
- **构建工具**: Vite 4
- **UI样式**: Tailwind CSS
- **动画库**: Framer Motion
- **图标库**: Lucide React
- **路由**: React Router DOM
- **HTTP客户端**: Axios
- **日期处理**: Day.js
- **AI集成**: Dify API

## 📋 系统要求

- Node.js >= 16.0.0
- npm >= 8.0.0 或 yarn >= 1.22.0
- 现代浏览器支持ES2020+

## 🛠️ 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd health-assitant-agent
```

### 2. 安装依赖
```bash
# 使用npm
npm install

# 或使用yarn
yarn install
```

### 3. 环境配置
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件，填入Dify API配置
nano .env
```

### 4. 启动开发服务器
```bash
npm run dev
```

### 5. 打开浏览器访问
```
http://localhost:3000
```

## ⚙️ 环境配置

### Dify平台配置
```env
# Dify API基础配置
VITE_DIFY_BASE_URL=https://api.dify.ai/v1
VITE_DIFY_API_KEY=your_dify_api_key

# 智能体应用ID
VITE_DIFY_REPORT_APP_ID=your_report_agent_app_id
VITE_DIFY_DOCTOR_APP_ID=your_doctor_agent_app_id
```

### 应用配置
```env
# 应用基础信息
VITE_APP_NAME=个人健康助理
VITE_APP_VERSION=1.0.0

# API配置
VITE_API_TIMEOUT=30000
VITE_API_RETRY_COUNT=3

# 文件上传配置
VITE_UPLOAD_MAX_SIZE=10
VITE_UPLOAD_MAX_FILES=5
```

## 🔧 开发指南

### 项目结构
```
src/
├── components/          # 公共组件
│   └── Layout.tsx      # 布局组件
├── pages/              # 页面组件
│   ├── Home.tsx        # 首页
│   ├── ReportAnalysis.tsx    # 报告解读
│   ├── HealthConsultation.tsx # 健康咨询
│   ├── SymptomAssessment.tsx  # 症状自评
│   └── TrendAnalysis.tsx      # 趋势分析
├── utils/              # 工具函数
│   ├── api.ts          # API封装
│   └── index.ts        # 通用工具
├── config/             # 配置文件
│   └── index.ts        # 应用配置
├── App.tsx             # 根组件
├── main.tsx            # 入口文件
└── index.css           # 全局样式
```

### 代码规范
- 使用TypeScript严格模式
- 遵循React Hooks最佳实践
- 组件采用函数式编程
- 所有函数和组件都有详细中文注释
- 使用Prettier进行代码格式化

### API集成
```typescript
// Dify API调用示例
import { difyClient } from '@/utils/api'

const response = await difyClient.sendMessage(
  appId,           // 智能体应用ID
  message,         // 用户消息
  conversationId,  // 会话ID（可选）
  files           // 文件列表（可选）
)
```

## 🐳 Docker部署

### 快速部署

项目提供了完整的自动化部署脚本：

```bash
# 给脚本执行权限
chmod +x build.sh wrap.sh

# 一键构建和部署 (版本号 环境 操作)
./wrap.sh 1.0.0 dev all
```

### 分步部署

#### 1. 构建前端应用
```bash
# 构建开发环境
./build.sh 1.0.0 dev

# 构建测试环境
./build.sh 1.0.0 test

# 构建生产环境
./build.sh 1.0.0 prod
```

#### 2. Docker镜像操作
```bash
# 构建镜像
./wrap.sh 1.0.0 dev build

# 推送到仓库
./wrap.sh 1.0.0 dev push

# 部署容器
./wrap.sh 1.0.0 dev deploy
```

#### 3. 手动Docker操作
```bash
# 构建镜像 (支持多架构)
docker build --platform linux/amd64 \
  -f Dockerfile \
  -t registry.cn-shanghai.aliyuncs.com/chos/health-assist-agent-frontend-dev:1.0.0 .

# 运行容器
docker rm -f health-assist-agent-frontend-dev 2>/dev/null || true
docker run --restart always -d \
  -p 8060:80 \
  -e TZ=Asia/Shanghai \
  --name health-assist-agent-frontend-dev \
  registry.cn-shanghai.aliyuncs.com/chos/health-assist-agent-frontend-dev:1.0.0
```

### 环境配置

不同环境使用不同的端口：

- **开发环境 (dev)**: 端口 8060
- **测试环境 (test)**: 端口 8061
- **生产环境 (prod)**: 端口 8062

### 健康检查

部署后可以通过以下端点检查应用状态：

```bash
# 健康检查
curl http://localhost:8060/health

# 版本信息
curl http://localhost:8060/version
```

### 脚本说明

#### build.sh
- 前端应用构建脚本
- 支持多环境构建 (dev/test/prod)
- 自动类型检查和代码格式检查
- 生成版本信息文件

#### wrap.sh
- Docker镜像构建和部署脚本
- 支持的操作：build, push, deploy, all
- 自动容器管理和健康检查
- 多环境配置支持

## 📊 性能优化

### 构建优化
- Vite构建优化，支持代码分割
- 静态资源压缩和缓存
- Tree-shaking去除未使用代码
- 图片资源优化

### 运行时优化
- React.memo优化组件渲染
- 懒加载和代码分割
- 本地存储减少网络请求
- Service Worker缓存策略

## 🛡️ 安全说明

### 数据安全
- 所有健康数据仅在本地存储
- 不会上传敏感个人信息到服务器
- API通信使用HTTPS加密
- 严格的内容安全策略(CSP)

### 隐私保护
- 用户数据本地化存储
- 可选择性数据清除
- 符合GDPR隐私保护规范
- 透明的数据使用说明

## 🔍 故障排除

### 常见问题

1. **Dify API连接失败**
   ```bash
   # 检查网络连接
   curl -X GET "https://api.dify.ai/v1/info"
   
   # 验证API Key
   curl -H "Authorization: Bearer YOUR_API_KEY" \
        "https://api.dify.ai/v1/info"
   ```

2. **构建失败**
   ```bash
   # 清除缓存重新安装
   rm -rf node_modules package-lock.json
   npm install
   
   # 检查Node.js版本
   node --version  # 应该 >= 16.0.0
   ```

3. **Docker构建问题**
   ```bash
   # 清除Docker缓存
   docker system prune -a
   
   # 重新构建
   docker build --no-cache -t health-assistant:latest .
   ```

### 日志调试
```bash
# 开启开发模式详细日志
VITE_DEV_LOG_LEVEL=debug npm run dev

# Docker容器日志
docker logs health-assistant-app

# Nginx访问日志
docker exec health-assistant-app tail -f /var/log/nginx/access.log
```

## 📈 更新日志

### v1.0.0 (2024-01-20)
- ✨ 初始版本发布
- 🩺 报告解读功能完成
- 👨‍⚕️ AI健康咨询上线
- 📊 症状自评系统
- 📈 健康趋势分析
- 🐳 Docker部署支持
- 📱 PWA移动端优化


**个人健康助理** - 让健康管理更智能、更便捷 💙
