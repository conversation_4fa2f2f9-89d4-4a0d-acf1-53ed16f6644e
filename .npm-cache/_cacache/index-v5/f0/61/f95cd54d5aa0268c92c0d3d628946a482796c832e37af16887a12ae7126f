
0e849e85596987ffa44aebd40f9f123164466b32	{"key":"make-fetch-happen:request-cache:https://cdn.npmmirror.com/packages/micromark-util-decode-string/2.0.1/micromark-util-decode-string-2.0.1.tgz","integrity":"sha512-nDV/77Fj6eH1ynwscYTOsbK7rR//Uj0bZXBwJZRfaLEJ1iGBR6kIfNmlNqaqJf649EP0F3NWNdeJi03elllNUQ==","time":1750214317923,"size":3799,"metadata":{"time":1750214317812,"url":"https://cdn.npmmirror.com/packages/micromark-util-decode-string/2.0.1/micromark-util-decode-string-2.0.1.tgz","reqHeaders":{},"resHeaders":{"cache-control":"max-age=86400","content-type":"application/octet-stream","date":"Tue, 17 Jun 2025 11:46:46 GMT","etag":"\"DEB915228FEDE5DAFFA00EA4CB34BC6B\"","last-modified":"Tue, 12 Nov 2024 12:02:37 GMT"},"options":{"compress":true}}}