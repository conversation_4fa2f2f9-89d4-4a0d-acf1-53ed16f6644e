#!/bin/bash

# 简化版构建脚本 - 用于测试
set -e

VERSION=${1:-1.0.0}
ENVIRONMENT=${2:-dev}

echo "🔨 开始构建健康助理前端应用"
echo "📦 版本: $VERSION"
echo "🌍 环境: $ENVIRONMENT"

# 清理构建产物
echo "🧹 清理之前的构建产物..."
rm -rf dist/

# 构建应用
echo "⚡ 开始构建应用..."
npm run build

# 检查构建结果
if [ ! -d "dist" ]; then
    echo "❌ 构建失败，dist目录不存在"
    exit 1
fi

if [ ! -f "dist/index.html" ]; then
    echo "❌ 构建失败，index.html文件不存在"
    exit 1
fi

# 显示构建产物大小
echo "📊 构建产物分析:"
du -sh dist/
find dist/ -name "*.js" -o -name "*.css" | xargs ls -lh

# 创建版本信息文件
cat > dist/version.json << EOF
{
  "version": "$VERSION",
  "environment": "$ENVIRONMENT",
  "buildTime": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "gitCommit": "$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')",
  "gitBranch": "$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo 'unknown')"
}
EOF

echo "✅ 构建完成!"
echo "📁 构建产物位置: ./dist/"
echo "🕒 构建时间: $(date)"
