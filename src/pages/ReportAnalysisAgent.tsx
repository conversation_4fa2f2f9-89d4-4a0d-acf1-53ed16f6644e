import { AnimatePresence, motion } from 'framer-motion'
import {
    Activity,
    AlertCircle,
    AlertTriangle,
    Brain,
    Calendar,
    Camera,
    CheckCircle,
    ChevronDown,
    ChevronUp,
    FileText,
    Heart,
    Loader2,
    MessageCircle,
    Shield,
    TrendingUp,
    Upload,
    User
} from 'lucide-react'
import React, { useRef, useState } from 'react'
import { useNavigate } from 'react-router-dom'

// 工具和API
import { config, configUtils } from '@/config'
import { apiUtils, difyClient } from '@/utils/api'

// 🔥 本地类型定义（避免导入问题）
interface AgentThought {
  id: string
  message_id: string
  position: number
  thought: string
  tool: string | null
  tool_input: string | null
  created_at: number
}

// 解读结果接口
interface AnalysisResult {
  overview: {
    patientName: string
    age: number
    date: string
    overallScore: number
    riskLevel: 'low' | 'medium' | 'high'
    summary: string
  }
  indicators: {
    category: string
    status: 'normal' | 'attention' | 'abnormal'
    items: {
      name: string
      value: string
      normalRange: string
      status: 'normal' | 'attention' | 'abnormal'
      description: string
    }[]
  }[]
  healthAdvice: {
    dietary: string[]
    exercise: string[]
    lifestyle: string[]
  }
  riskWarnings: {
    title: string
    description: string
    preventionMeasures: string[]
  }[]
}

/**
 * 拍照解读智能体页面
 * 支持拍照和上传，专门用于体检报告解读
 */
const ReportAnalysisAgent: React.FC = () => {
  const navigate = useNavigate()

  // 状态管理
  const [step, setStep] = useState<'upload' | 'analyzing' | 'result'>('upload')
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null)
  const [rawAnalysis, setRawAnalysis] = useState<string>('')
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [error, setError] = useState<string>('')
  const [conversationId, setConversationId] = useState<string>('')
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set())
  
  // 🔥 新增：流式响应和思考过程状态
  const [thoughts, setThoughts] = useState<AgentThought[]>([])
  const [isThinking, setIsThinking] = useState(false)
  const [thoughtsCollapsed, setThoughtsCollapsed] = useState(true)
  const [streamingContent, setStreamingContent] = useState<string>('')
  const [analysisProgress, setAnalysisProgress] = useState<{
    overview: boolean
    indicators: boolean  
    healthAdvice: boolean
    riskWarnings: boolean
  }>({ overview: false, indicators: false, healthAdvice: false, riskWarnings: false })

  // 引用
  const fileInputRef = useRef<HTMLInputElement>(null)
  const cameraInputRef = useRef<HTMLInputElement>(null)

  // 处理文件选择（上传）
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    console.log(`📁 选择了 ${files.length} 个文件`)

    // 验证文件
    const validFiles = files.filter(file => {
      if (!apiUtils.validateFileType(file)) {
        console.warn(`❌ 不支持的文件类型: ${file.type}`)
        return false
      }
      if (!apiUtils.validateFileSize(file)) {
        console.warn(`❌ 文件过大: ${apiUtils.formatFileSize(file.size)}`)
        return false
      }
      return true
    })

    if (validFiles.length !== files.length) {
      setError('部分文件格式不支持或文件过大，请检查后重新选择')
      return
    }

    if (validFiles.length > config.upload.maxFiles) {
      setError(`最多只能选择 ${config.upload.maxFiles} 个文件`)
      return
    }

    setSelectedFiles(validFiles)
    setError('')

    if (validFiles.length > 0) {
      startAnalysis(validFiles)
    }
  }

  // 处理拍照
  const handleCameraCapture = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    if (files.length > 0) {
      console.log('📷 拍照完成，文件数量:', files.length)

      // 验证文件
      const validFiles = files.filter(file => {
        if (!apiUtils.validateFileType(file)) {
          console.warn(`❌ 不支持的文件类型: ${file.type}`)
          return false
        }
        if (!apiUtils.validateFileSize(file)) {
          console.warn(`❌ 文件过大: ${apiUtils.formatFileSize(file.size)}`)
          return false
        }
        return true
      })

      if (validFiles.length !== files.length) {
        setError('部分文件格式不支持或文件过大，请重新拍照')
        return
      }

      setSelectedFiles(validFiles)
      setError('')
      startAnalysis(validFiles)
    }
  }

  // 🔥 新增：思考过程处理组件
  const ThoughtProcess: React.FC<{ thoughts: AgentThought[], isThinking: boolean, collapsed: boolean, onToggle: () => void }> = ({ thoughts, isThinking, collapsed, onToggle }) => {
    if (thoughts.length === 0 && !isThinking) return null

    return (
      <div className="bg-purple-50 rounded-xl border border-purple-200 mb-4">
        <button
          onClick={onToggle}
          className="w-full p-4 flex items-center justify-between hover:bg-purple-100 transition-colors rounded-xl"
        >
          <div className="flex items-center space-x-2">
            <Brain className="w-5 h-5 text-purple-600" />
            <span className="font-medium text-purple-900">AI思考过程</span>
            {isThinking && <Loader2 className="w-4 h-4 text-purple-600 animate-spin" />}
          </div>
          {collapsed ? (
            <ChevronDown className="w-5 h-5 text-purple-600" />
          ) : (
            <ChevronUp className="w-5 h-5 text-purple-600" />
          )}
        </button>
        
        {!collapsed && (
          <div className="px-4 pb-4 space-y-2">
            {thoughts.map((thought, index) => (
              <div key={thought.id} className="bg-white rounded-lg p-3 border border-purple-100">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <span className="text-sm font-medium text-purple-900">{thought.tool || 'AI分析'}</span>
                </div>
                <div className="text-sm text-gray-700 whitespace-pre-wrap">
                  {thought.thought.replace('###THINKING_COMPLETED###', '')}
                </div>
              </div>
            ))}
            {isThinking && thoughts.length === 0 && (
              <div className="flex items-center space-x-2 text-purple-700">
                <Loader2 className="w-4 h-4 animate-spin" />
                <span className="text-sm">正在思考中...</span>
              </div>
            )}
          </div>
        )}
      </div>
    )
  }

  // 开始分析
  const startAnalysis = async (files: File[]) => {
    if (!configUtils.isAgentConfigured('report')) {
      setError('报告解读服务未配置，请联系管理员')
      return
    }

    // 🔥 检查API Key配置
    if (config.dify.apiKey === 'app-test-key-123') {
      setError('请配置正确的Dify API Key，当前使用的是测试密钥')
      console.error('❌ 使用测试API Key，无法连接真实的Dify服务')
      return
    }

    setStep('analyzing')
    setIsAnalyzing(true)
    setError('')
    setThoughts([])
    setIsThinking(true)
    setStreamingContent('')
    setAnalysisProgress({ overview: false, indicators: false, healthAdvice: false, riskWarnings: false })

    try {
      console.log('🔍 开始分析报告，使用APP ID:', config.dify.reportAgent.appId)

      // 构建专业的分析提示词，要求返回结构化数据
      const prompt = `
请作为专业的医学检验专家，仔细分析这份体检报告。

<think>
请你按以下步骤思考：
1. 首先仔细识别和解读报告中的所有检验项目和数值
2. 分析每个指标是否在正常范围内，判断异常程度
3. 综合评估患者的整体健康状况
4. 根据异常指标提出针对性的健康建议和风险预警
</think>

请按以下JSON格式严格返回结构化结果：

{
  "overview": {
    "patientName": "患者姓名",
    "age": 35,
    "date": "2024-12-01",
    "overallScore": 93,
    "riskLevel": "low",
    "summary": "综合健康评估总结"
  },
  "indicators": [
    {
      "category": "血常规",
      "status": "attention",
      "items": [
        {
          "name": "白细胞计数",
          "value": "6.8 ×10⁹/L",
          "normalRange": "3.5-9.5×10⁹/L",
          "status": "normal",
          "description": "指标意义和健康影响说明"
        }
      ]
    }
  ],
  "healthAdvice": {
    "dietary": ["维生素C补充", "富含铁质食物"],
    "exercise": ["有氧运动", "每周至少150分钟"],
    "lifestyle": ["充足睡眠", "减少感染风险"]
  },
  "riskWarnings": [
    {
      "title": "白细胞计数异常",
      "description": "可能存在感染、炎症或血液系统疾病",
      "preventionMeasures": ["充足睡眠", "避免过度疲劳", "减少感染风险"]
    }
  ]
}

请确保：
1. 根据实际报告内容填写数据，如果某些信息无法从报告中获取，请合理推测或标注"未提供"
2. overallScore为0-100的综合健康评分
3. riskLevel为：low（低风险）、medium（中风险）、high（高风险）
4. status为：normal（正常）、attention（关注）、abnormal（异常）
5. 健康建议要具体实用，风险预警要准确专业
6. 只返回JSON数据，不要其他说明文字
`

      // 🔥 使用与健康咨询页面相同的API调用方式
      await difyClient.sendMessageWithImages(
        config.dify.reportAgent.appId,
        prompt,
        files,
        {
          onMessage: (content: string, isComplete: boolean) => {
            console.log('📊 接收到分析内容，长度:', content.length)
            setStreamingContent(content)

            // 🔥 异步解析部分结果，实现增量更新
            if (content && !isComplete) {
              try {
                const jsonMatch = content.match(/\{[\s\S]*\}/)
                if (jsonMatch) {
                  const partialResult = JSON.parse(jsonMatch[0])

                  // 更新进度状态
                  setAnalysisProgress(prev => ({
                    overview: !!partialResult.overview,
                    indicators: !!(partialResult.indicators && partialResult.indicators.length > 0),
                    healthAdvice: !!partialResult.healthAdvice,
                    riskWarnings: !!(partialResult.riskWarnings && partialResult.riskWarnings.length > 0)
                  }))

                  // 如果有完整的部分数据，可以先显示
                  if (partialResult.overview && partialResult.indicators) {
                    setAnalysisResult(partialResult)
                  }
                }
              } catch (e) {
                // 忽略解析错误，继续等待完整数据
                console.log('🔄 等待完整数据...')
              }
            }
          },
          onThought: (thought: AgentThought) => {
            console.log('🧠 收到思考过程:', thought.thought.substring(0, 100))

            // 检查是否包含思考完成标记
            if (thought.thought.includes('###THINKING_COMPLETED###')) {
              setIsThinking(false)
              console.log('🧠 思考过程完成')
            }

            setThoughts(prev => {
              // 更新现有的思考过程，而不是添加新的
              const existing = prev.find(t => t.id === thought.id)
              if (existing) {
                return prev.map(t => t.id === thought.id ? thought : t)
              } else {
                return [...prev, thought]
              }
            })
          },
          onError: (error: Error) => {
            console.error('🚨 分析过程中出错:', error)
            setError(`分析失败: ${error.message}`)
            setIsThinking(false)
            setStep('upload') // 返回上传页面
          },
          onComplete: (response) => {
            console.log('✅ 报告分析完成')
            setIsThinking(false)

            try {
              const finalContent = response.answer || ''
              console.log('📋 最终分析结果长度:', finalContent.length)

              // 解析JSON结果
              const jsonMatch = finalContent.match(/\{[\s\S]*\}/)
              if (jsonMatch) {
                const result = JSON.parse(jsonMatch[0])
                setAnalysisResult(result)
                setStep('result')
                console.log('✅ JSON解析成功')
              } else {
                // 如果無法解析JSON，保存原始文本
                setRawAnalysis(finalContent)
                setStep('result')
                console.log('⚠️ 未找到JSON格式，使用原始文本')
              }
            } catch (parseError) {
              console.warn('⚠️ JSON解析失败，使用原始文本:', parseError)
              setRawAnalysis(response.answer || '解析失败')
              setStep('result')
            }
            setConversationId(response.conversation_id)
          }
        },
        conversationId
      )

    } catch (error) {
      console.error('🚨 分析失败:', error)
      const errorMessage = error instanceof Error ? error.message : '分析失败，请稍后重试'

      // 🔥 开发环境下提供mock数据，便于测试UI
      if (import.meta.env.DEV) {
        console.log('🔧 开发环境：使用mock数据进行测试')
        const mockResult: AnalysisResult = {
          overview: {
            patientName: "张三",
            age: 35,
            date: "2024-12-01",
            overallScore: 85,
            riskLevel: "medium",
            summary: "整体健康状况良好，部分指标需要关注。建议定期复查，注意饮食和运动。"
          },
          indicators: [
            {
              category: "血常规",
              status: "attention",
              items: [
                {
                  name: "白细胞计数",
                  value: "10.2 ×10⁹/L",
                  normalRange: "3.5-9.5×10⁹/L",
                  status: "attention",
                  description: "白细胞计数略高，可能提示轻微感染或炎症反应"
                },
                {
                  name: "红细胞计数",
                  value: "4.5 ×10¹²/L",
                  normalRange: "4.3-5.8×10¹²/L",
                  status: "normal",
                  description: "红细胞计数正常，氧气运输功能良好"
                }
              ]
            },
            {
              category: "生化指标",
              status: "normal",
              items: [
                {
                  name: "血糖",
                  value: "5.2 mmol/L",
                  normalRange: "3.9-6.1 mmol/L",
                  status: "normal",
                  description: "空腹血糖正常，糖代谢功能良好"
                }
              ]
            }
          ],
          healthAdvice: {
            dietary: ["增加维生素C摄入", "多吃新鲜蔬果", "减少高糖食物"],
            exercise: ["每周至少150分钟中等强度运动", "增加有氧运动", "适当力量训练"],
            lifestyle: ["保证充足睡眠", "减少压力", "定期体检"]
          },
          riskWarnings: [
            {
              title: "轻微炎症指标",
              description: "白细胞计数略高，可能存在轻微感染或炎症",
              preventionMeasures: ["充足休息", "多喝水", "避免过度疲劳", "如症状持续请及时就医"]
            }
          ]
        }

        setAnalysisResult(mockResult)
        setStep('result')
        setError('') // 清除错误，使用mock数据
      } else {
        setError(errorMessage)
        setStep('upload')
      }
      setIsThinking(false)
    } finally {
      setIsAnalyzing(false)
    }
  }

  // 重新开始
  const restart = () => {
    setStep('upload')
    setSelectedFiles([])
    setAnalysisResult(null)
    setRawAnalysis('')
    setError('')
    setConversationId('')
    setExpandedCategories(new Set())
    
    // 🔥 新增：重置流式响应和思考过程状态
    setThoughts([])
    setIsThinking(false)
    setThoughtsCollapsed(true)
    setStreamingContent('')
    setAnalysisProgress({ overview: false, indicators: false, healthAdvice: false, riskWarnings: false })

    // 重置文件输入
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
    if (cameraInputRef.current) {
      cameraInputRef.current.value = ''
    }
  }

  // 切换指标分类展开状态
  const toggleCategory = (category: string) => {
    const newExpanded = new Set(expandedCategories)
    if (newExpanded.has(category)) {
      newExpanded.delete(category)
    } else {
      newExpanded.add(category)
    }
    setExpandedCategories(newExpanded)
  }

  // 获取状态对应的颜色和图标
  const getStatusInfo = (status: 'normal' | 'attention' | 'abnormal') => {
    switch (status) {
      case 'normal':
        return { color: 'text-green-600', bgColor: 'bg-green-50', icon: CheckCircle, label: '正常' }
      case 'attention':
        return { color: 'text-yellow-600', bgColor: 'bg-yellow-50', icon: AlertTriangle, label: '关注' }
      case 'abnormal':
        return { color: 'text-red-600', bgColor: 'bg-red-50', icon: AlertCircle, label: '异常' }
    }
  }

  // 获取风险等级信息
  const getRiskLevelInfo = (level: 'low' | 'medium' | 'high') => {
    switch (level) {
      case 'low':
        return { color: 'text-green-600', bgColor: 'bg-green-100', label: '低风险' }
      case 'medium':
        return { color: 'text-yellow-600', bgColor: 'bg-yellow-100', label: '中风险' }
      case 'high':
        return { color: 'text-red-600', bgColor: 'bg-red-100', label: '高风险' }
    }
  }

  return (
    <div className="space-y-4 sm:space-y-6 pt-6">
      {/* 标题栏 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3" style={{ width: '100%', textAlign: 'center' }}>
          <h1 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900">报告解读</h1>
          <p className="text-sm text-gray-600">AI智能分析，专业医学解读</p>
        </div>
      </div>

      <AnimatePresence mode="wait">
        {/* 上传阶段 */}
        {step === 'upload' && (
          <motion.div
            key="upload"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <div className="text-center">
              <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <FileText className="w-10 h-10 text-blue-600" />
              </div>
              <h2 className="text-xl font-bold text-gray-900 mb-2">上传报告图片</h2>
              <p className="text-gray-600">支持拍照或从相册选择，AI将为您专业解读</p>
            </div>

            {/* 上传选项 */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {/* 拍照选项 */}
              <button
                onClick={() => cameraInputRef.current?.click()}
                className="p-6 border-2 border-dashed border-blue-300 rounded-2xl text-center hover:border-blue-400 hover:bg-blue-50 transition-colors"
              >
                <Camera className="w-12 h-12 text-blue-500 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-900 mb-1">拍照解读</h3>
                <p className="text-sm text-gray-600">实时拍摄体检/检查/检验报告</p>
              </button>

              {/* 上传选项 */}
              <button
                onClick={() => fileInputRef.current?.click()}
                className="p-6 border-2 border-dashed border-green-300 rounded-2xl text-center hover:border-green-400 hover:bg-green-50 transition-colors"
              >
                <Upload className="w-12 h-12 text-green-500 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-900 mb-1">选择图片</h3>
                <p className="text-sm text-gray-600">从相册选择体检/检查/检验报告</p>
              </button>
            </div>

            {/* 隐藏的文件输入 */}
            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept="image/*"
              onChange={handleFileSelect}
              className="hidden"
            />
            <input
              ref={cameraInputRef}
              type="file"
              accept="image/*"
              capture="environment"
              onChange={handleCameraCapture}
              className="hidden"
            />

            {/* 使用说明 */}
            <div className="bg-blue-50 rounded-2xl p-4">
              <h4 className="font-medium text-blue-900 mb-3 flex items-center">
                <Shield className="w-4 h-4 mr-2" />
                支持报告类型
              </h4>
              <div className="grid grid-cols-2 gap-3 text-sm text-blue-700 mb-3">
                <div className="space-y-1">
                  <div className="font-medium">血液检查</div>
                  <div>• 血常规检查</div>
                  <div>• 生化全套</div>
                  <div>• 血脂检查</div>
                  <div>• 血糖检测</div>
                </div>
                <div className="space-y-1">
                  <div className="font-medium">其他检查</div>
                  <div>• 尿常规检查</div>
                  <div>• 肝功能检查</div>
                  <div>• 肾功能检查</div>
                  <div>• 体检报告</div>
                </div>
              </div>
              <div className="text-xs text-blue-600 space-y-1 border-t border-blue-200 pt-2">
                <div>📱 支持JPG、PNG格式，最大10MB</div>
                <div>📋 请确保图片清晰，文字可见</div>
                <div>🔒 数据安全处理，隐私保护</div>
              </div>
            </div>

            {/* 🔥 开发环境下的测试按钮 */}
            {import.meta.env.DEV && (
              <div className="bg-yellow-50 rounded-2xl p-4 border border-yellow-200">
                <h4 className="font-medium text-yellow-900 mb-3">🔧 开发测试</h4>
                <button
                  onClick={() => {
                    console.log('🧪 测试mock数据')
                    // 创建一个虚拟文件来触发分析
                    const mockFile = new File(['mock'], 'test-report.jpg', { type: 'image/jpeg' })
                    startAnalysis([mockFile])
                  }}
                  className="w-full bg-yellow-100 hover:bg-yellow-200 text-yellow-800 py-2 px-4 rounded-lg transition-colors"
                >
                  测试结构化数据展示
                </button>
                <div className="text-xs text-yellow-700 mt-2">
                  点击此按钮将使用mock数据测试UI展示效果
                </div>
              </div>
            )}

            {error && (
              <div className="flex items-center space-x-2 text-red-600 bg-red-50 p-3 rounded-xl">
                <AlertCircle className="w-5 h-5" />
                <span className="text-sm">{error}</span>
              </div>
            )}
          </motion.div>
        )}

        {/* 分析阶段 */}
        {step === 'analyzing' && (
          <motion.div
            key="analyzing"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <div className="text-center">
              <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Loader2 className="w-10 h-10 text-blue-600 animate-spin" />
              </div>
              <h2 className="text-xl font-bold text-gray-900 mb-2">AI正在解读中...</h2>
              <p className="text-gray-600">正在生成专业解读报告...</p>
            </div>

            {/* 🔥 新增：思考过程显示 */}
            <ThoughtProcess 
              thoughts={thoughts}
              isThinking={isThinking}
              collapsed={thoughtsCollapsed}
              onToggle={() => setThoughtsCollapsed(!thoughtsCollapsed)}
            />

            {/* 🔥 增强：进度指示器 - 显示各个数据区域的分析进度 */}
            <div className="space-y-3">
              <div className="flex items-center text-green-600">
                <CheckCircle className="w-5 h-5 mr-3" />
                <span className="text-sm">文件上传完成</span>
              </div>
              <div className="flex items-center text-green-600">
                <CheckCircle className="w-5 h-5 mr-3" />
                <span className="text-sm">OCR识别完成</span>
              </div>
              <div className={`flex items-center ${isThinking ? 'text-blue-600' : 'text-green-600'}`}>
                {isThinking ? (
                  <Loader2 className="w-5 h-5 mr-3 animate-spin" />
                ) : (
                  <CheckCircle className="w-5 h-5 mr-3" />
                )}
                <span className="text-sm">正在生成专业解读报告...</span>
              </div>
              
              {/* 🔥 新增：各个数据区域的分析进度 */}
              <div className="ml-6 space-y-2 text-sm">
                <div className={`flex items-center ${analysisProgress.overview ? 'text-green-600' : 'text-gray-400'}`}>
                  {analysisProgress.overview ? (
                    <CheckCircle className="w-4 h-4 mr-2" />
                  ) : (
                    <div className="w-4 h-4 mr-2 border-2 border-gray-300 rounded-full"></div>
                  )}
                  <span>综合健康评估</span>
                </div>
                <div className={`flex items-center ${analysisProgress.indicators ? 'text-green-600' : 'text-gray-400'}`}>
                  {analysisProgress.indicators ? (
                    <CheckCircle className="w-4 h-4 mr-2" />
                  ) : (
                    <div className="w-4 h-4 mr-2 border-2 border-gray-300 rounded-full"></div>
                  )}
                  <span>指标详细分析</span>
                </div>
                <div className={`flex items-center ${analysisProgress.healthAdvice ? 'text-green-600' : 'text-gray-400'}`}>
                  {analysisProgress.healthAdvice ? (
                    <CheckCircle className="w-4 h-4 mr-2" />
                  ) : (
                    <div className="w-4 h-4 mr-2 border-2 border-gray-300 rounded-full"></div>
                  )}
                  <span>健康建议生成</span>
                </div>
                <div className={`flex items-center ${analysisProgress.riskWarnings ? 'text-green-600' : 'text-gray-400'}`}>
                  {analysisProgress.riskWarnings ? (
                    <CheckCircle className="w-4 h-4 mr-2" />
                  ) : (
                    <div className="w-4 h-4 mr-2 border-2 border-gray-300 rounded-full"></div>
                  )}
                  <span>风险预警评估</span>
                </div>
              </div>
            </div>

            {/* 预览上传的图片 */}
            {selectedFiles.length > 0 && (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                {selectedFiles.map((file, index) => (
                  <div key={index} className="relative">
                    <img
                      src={URL.createObjectURL(file)}
                      alt={`报告 ${index + 1}`}
                      className="w-full h-48 object-cover rounded-xl border border-gray-200"
                    />
                  </div>
                ))}
              </div>
            )}
          </motion.div>
        )}

        {/* 结果阶段 */}
        {step === 'result' && (
          <motion.div
            key="result"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {/* 🔥 新增：在结果页面也显示思考过程（如果有的话） */}
            {thoughts.length > 0 && (
              <ThoughtProcess 
                thoughts={thoughts}
                isThinking={false}
                collapsed={thoughtsCollapsed}
                onToggle={() => setThoughtsCollapsed(!thoughtsCollapsed)}
              />
            )}

            {analysisResult ? (
              // 结构化结果显示
              <>
                {/* 报告概览 */}
                <div className="bg-white rounded-2xl border border-gray-200 p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-xl font-bold text-gray-900 flex items-center">
                      <FileText className="w-5 h-5 mr-2 text-blue-600" />
                      体检报告综合解读
                    </h2>
                    <div className={`px-3 py-1 rounded-full text-sm font-medium ${getRiskLevelInfo(analysisResult.overview.riskLevel).bgColor} ${getRiskLevelInfo(analysisResult.overview.riskLevel).color}`}>
                      {getRiskLevelInfo(analysisResult.overview.riskLevel).label}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <User className="w-4 h-4 text-gray-500" />
                        <span className="text-sm text-gray-600">患者姓名：</span>
                        <span className="font-medium">{analysisResult.overview.patientName}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Calendar className="w-4 h-4 text-gray-500" />
                        <span className="text-sm text-gray-600">检查日期：</span>
                        <span className="font-medium">{analysisResult.overview.date}</span>
                      </div>
                    </div>

                    <div className="text-center">
                      <div className="text-4xl font-bold text-green-600 mb-1">
                        {analysisResult.overview.overallScore}
                      </div>
                      <div className="text-sm text-gray-600">综合健康评分</div>
                    </div>
                  </div>

                  <div className="mt-6 p-4 bg-blue-50 rounded-xl">
                    <h3 className="font-semibold text-blue-900 mb-2">总体评估</h3>
                    <p className="text-blue-800 text-sm leading-relaxed">{analysisResult.overview.summary}</p>
                  </div>

                  {/* 快捷操作 */}
                  <div className="mt-6 flex space-x-4">
                    <button
                      onClick={() => navigate('/consultation', { state: { initialMessage: `我想咨询一下我的体检报告结果，综合健康评分是${analysisResult.overview.overallScore}分` } })}
                      className="flex-1 btn-primary"
                    >
                      <MessageCircle className="w-4 h-4 mr-2" />
                      AI健康咨询
                    </button>
                    <button className="flex-1 btn-secondary">
                      <FileText className="w-4 h-4 mr-2" />
                      详细报告
                    </button>
                  </div>
                </div>

                {/* 指标分析 */}
                <div className="bg-white rounded-2xl border border-gray-200 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <Activity className="w-5 h-5 mr-2 text-purple-600" />
                    指标分析
                  </h3>

                  <div className="space-y-4">
                    {analysisResult.indicators.map((category, index) => {
                      const statusInfo = getStatusInfo(category.status)
                      const isExpanded = expandedCategories.has(category.category)

                      return (
                        <div key={index} className="border border-gray-200 rounded-xl overflow-hidden">
                          <button
                            onClick={() => toggleCategory(category.category)}
                            className="w-full p-4 bg-gray-50 hover:bg-gray-100 transition-colors flex items-center justify-between"
                          >
                            <div className="flex items-center space-x-3">
                              <statusInfo.icon className={`w-5 h-5 ${statusInfo.color}`} />
                              <span className="font-medium">{category.category}</span>
                              <span className={`px-2 py-1 rounded-full text-xs ${statusInfo.bgColor} ${statusInfo.color}`}>
                                {statusInfo.label}
                              </span>
                              <span className="text-sm text-gray-600">{category.items.length}项指标</span>
                            </div>
                            {isExpanded ? (
                              <ChevronUp className="w-4 h-4 text-gray-500" />
                            ) : (
                              <ChevronDown className="w-4 h-4 text-gray-500" />
                            )}
                          </button>

                          {isExpanded && (
                            <div className="p-4 space-y-3">
                              {category.items.map((item, itemIndex) => {
                                const itemStatusInfo = getStatusInfo(item.status)
                                return (
                                  <div key={itemIndex} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div className="flex-1">
                                      <div className="flex items-center space-x-2 mb-1">
                                        <itemStatusInfo.icon className={`w-4 h-4 ${itemStatusInfo.color}`} />
                                        <span className="font-medium text-sm">{item.name}</span>
                                        <span className={`px-2 py-0.5 rounded text-xs ${itemStatusInfo.bgColor} ${itemStatusInfo.color}`}>
                                          {itemStatusInfo.label}
                                        </span>
                                      </div>
                                      <div className="text-xs text-gray-600">
                                        {item.description}
                                      </div>
                                    </div>
                                    <div className="text-right text-sm">
                                      <div className="font-medium">{item.value}</div>
                                      <div className="text-xs text-gray-500">正常值: {item.normalRange}</div>
                                    </div>
                                  </div>
                                )
                              })}
                            </div>
                          )}
                        </div>
                      )
                    })}
                  </div>
                </div>

                {/* 健康建议 */}
                <div className="bg-white rounded-2xl border border-gray-200 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <Heart className="w-5 h-5 mr-2 text-green-600" />
                    健康建议
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {/* 饮食建议 */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                        <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                        饮食建议
                      </h4>
                      <ul className="space-y-2">
                        {analysisResult.healthAdvice.dietary.map((advice, index) => (
                          <li key={index} className="flex items-center text-sm text-gray-700">
                            <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                            {advice}
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* 运动建议 */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                        <div className="w-2 h-2 bg-purple-500 rounded-full mr-2"></div>
                        运动建议
                      </h4>
                      <ul className="space-y-2">
                        {analysisResult.healthAdvice.exercise.map((advice, index) => (
                          <li key={index} className="flex items-center text-sm text-gray-700">
                            <TrendingUp className="w-4 h-4 text-purple-500 mr-2 flex-shrink-0" />
                            {advice}
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* 生活方式 */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                        <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                        生活方式
                      </h4>
                      <ul className="space-y-2">
                        {analysisResult.healthAdvice.lifestyle.map((advice, index) => (
                          <li key={index} className="flex items-center text-sm text-gray-700">
                            <CheckCircle className="w-4 h-4 text-blue-500 mr-2 flex-shrink-0" />
                            {advice}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>

                {/* 风险预警 */}
                {analysisResult.riskWarnings.length > 0 && (
                  <div className="bg-white rounded-2xl border border-gray-200 p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                      <AlertTriangle className="w-5 h-5 mr-2 text-orange-600" />
                      风险预警
                    </h3>

                    <div className="space-y-4">
                      {analysisResult.riskWarnings.map((warning, index) => (
                        <div key={index} className="bg-orange-50 rounded-xl p-4 border border-orange-200">
                          <h4 className="font-medium text-orange-900 mb-2 flex items-center">
                            <AlertTriangle className="w-4 h-4 mr-2 text-orange-600" />
                            {warning.title}
                          </h4>
                          <p className="text-orange-800 text-sm mb-3">{warning.description}</p>
                          <div>
                            <h5 className="font-medium text-orange-900 text-sm mb-2">预防措施：</h5>
                            <ul className="space-y-1">
                              {warning.preventionMeasures.map((measure, measureIndex) => (
                                <li key={measureIndex} className="text-sm text-orange-700 flex items-center">
                                  <div className="w-1.5 h-1.5 bg-orange-500 rounded-full mr-2"></div>
                                  {measure}
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </>
            ) : (
              // 原始文本结果显示
              <div className="bg-white rounded-2xl border border-gray-200 p-6">
                <div className="text-center mb-6">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <CheckCircle className="w-8 h-8 text-green-600" />
                  </div>
                  <h2 className="text-xl font-bold text-gray-900 mb-2">分析完成</h2>
                  <p className="text-gray-600">以下是您的报告解读结果</p>
                </div>

                <div className="prose prose-sm max-w-none">
                  <div className="whitespace-pre-wrap text-gray-800 leading-relaxed bg-gray-50 p-4 rounded-xl">
                    {rawAnalysis}
                  </div>
                </div>
              </div>
            )}

            {/* 底部操作按钮 */}
            <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
              <button onClick={restart} className="flex-1 btn-secondary">
                <FileText className="w-5 h-5 mr-2" />
                分析新报告
              </button>
              <button
                onClick={() => {
                  // 保存结果到本地存储
                  const reportData = {
                    files: selectedFiles.map(f => f.name),
                    result: analysisResult || rawAnalysis,
                    timestamp: Date.now(),
                  }
                  const history = JSON.parse(localStorage.getItem('report_analysis_history') || '[]')
                  history.unshift(reportData)
                  localStorage.setItem('report_analysis_history', JSON.stringify(history.slice(0, 10)))

                  console.log('💾 报告解读结果已保存到本地')
                }}
                className="flex-1 btn-primary"
              >
                保存结果
              </button>
            </div>

            {/* 免责声明 */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-4">
              <div className="flex items-start space-x-3">
                <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5" />
                <div className="text-sm text-yellow-800">
                  <p className="font-medium mb-1">重要提示</p>
                  <p>此解读结果仅供参考，不能替代专业医生的诊断。如有疑问请及时咨询专业医师。</p>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default ReportAnalysisAgent 