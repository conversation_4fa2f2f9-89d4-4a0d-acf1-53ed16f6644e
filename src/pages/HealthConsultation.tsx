import { AnimatePresence, motion } from 'framer-motion'
import {
  Alert<PERSON>ircle,
  Bo<PERSON>,
  Brain,
  Check,
  ChevronDown,
  ChevronUp,
  Copy,
  Image as ImageIcon,
  Loader2,
  Send,
  ThumbsDown,
  ThumbsUp,
  User,
  X
} from 'lucide-react'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'

// 工具和API
import { config, configUtils } from '@/config'
import { dateUtils, storageUtils } from '@/utils'
import { difyClient, type AgentThought, type DifyMessage, type DifyResponse } from '@/utils/api'

// 🔥 本地类型定义（避免导入问题）
interface RAGCitation {
  title: string
  content: string
  source: string
  score?: number
}

interface MessageFeedback {
  rating: 'like' | 'dislike'
  content?: string
}

interface AppParameters {
  opening_statement?: string
  suggested_questions?: string[]
}

// 接口定义
interface ChatMessage extends DifyMessage {
  id: string
  difyMessageId?: string // 🔥 新增：Dify API返回的真实message_id，用于反馈功能
  loading?: boolean
  isStreaming?: boolean
  thoughts?: AgentThought[] // 思考过程
  thoughtsCollapsed?: boolean // 新增：思考过程是否折叠
  thinkingCompleted?: boolean // 新增：思考过程是否已完成
  hasStartedContent?: boolean // 🔥 新增：是否已开始接收正文内容
  citations?: RAGCitation[] // 🔥 新增：RAG知识库引用
  suggestedQuestions?: string[] // 🔥 新增：建议的下一步问题
  feedback?: MessageFeedback // 🔥 新增：用户反馈
  images?: File[] // 🔥 新增：附带的图片文件
}

// 本地存储数据接口
interface HealthConsultationData {
  messages: ChatMessage[]
  conversationId?: string
  userId?: string // 🔥 新增：保存用户ID
  timestamp: number
}

/**
 * 健康咨询页面
 * AI健康咨询聊天界面
 */
const HealthConsultation: React.FC = () => {
  // 状态管理
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [inputValue, setInputValue] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [conversationId, setConversationId] = useState('')
  const [copiedMessageId, setCopiedMessageId] = useState<string | null>(null)
  const [userId, setUserId] = useState('') // 🔥 新增：保存当前对话的用户ID
  
  // 🔥 新增：多模态和扩展功能状态
  const [selectedImages, setSelectedImages] = useState<File[]>([])
  const [appParameters, setAppParameters] = useState<AppParameters | null>(null)
  const [showSuggestedQuestions, setShowSuggestedQuestions] = useState(false)

  // 引用
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLTextAreaElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const userIdRef = useRef<string>('') // 🔥 新增：保存最新的userId值，避免闭包问题

  // 🔥 自动滚动到底部 - 优化逻辑，避免干扰用户手动滚动
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true) // 新增：是否应该自动滚动
  const [isUserScrolling, setIsUserScrolling] = useState(false) // 新增：用户是否在手动滚动
  const scrollTimeoutRef = useRef<NodeJS.Timeout>()

  // 检测用户是否在手动滚动
  useEffect(() => {
    const messagesContainer = messagesEndRef.current?.parentElement
    if (!messagesContainer) return

    const handleScroll = () => {
      setIsUserScrolling(true)
      
      // 清除之前的定时器
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current)
      }
      
      // 检查是否滚动到底部附近（容忍50px的误差）
      const { scrollTop, scrollHeight, clientHeight } = messagesContainer
      const isNearBottom = scrollHeight - scrollTop - clientHeight < 50
      
      if (isNearBottom) {
        setShouldAutoScroll(true)
      } else {
        setShouldAutoScroll(false)
      }
      
      // 1秒后重置用户滚动状态
      scrollTimeoutRef.current = setTimeout(() => {
        setIsUserScrolling(false)
      }, 1000)
    }

    messagesContainer.addEventListener('scroll', handleScroll, { passive: true })
    
    return () => {
      messagesContainer.removeEventListener('scroll', handleScroll)
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current)
      }
    }
  }, [])

  // 修复自动滚动逻辑 - 减少跳跃
  useEffect(() => {
    if (!shouldAutoScroll || isUserScrolling) return

    const lastMessage = messages[messages.length - 1]
    const shouldScroll = 
      messages.length > 0 && (
        lastMessage?.isStreaming || 
        lastMessage?.loading || 
        Date.now() - new Date(lastMessage?.timestamp || 0).getTime() < 3000
      )

    if (shouldScroll) {
      // 🔥 修复：使用节流机制，避免频繁滚动，但缩短延迟时间
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current)
      }
      
      scrollTimeoutRef.current = setTimeout(() => {
        const element = messagesEndRef.current
        if (element) {
          // 🔥 修复：只使用一种滚动方式，避免冲突
          const container = element.parentElement
          if (container) {
            // 检查是否已经在底部附近（容忍50px误差）
            const { scrollTop, scrollHeight, clientHeight } = container
            const isNearBottom = scrollHeight - scrollTop - clientHeight < 50
            
            if (!isNearBottom) {
              // 🔥 修复：对于流式消息使用瞬时滚动，避免干扰用户阅读
              const behavior = lastMessage?.isStreaming ? 'auto' : 'smooth'
              container.scrollTo({
                top: scrollHeight,
                behavior
              })
            }
          }
        }
      }, 100) // 🔥 减少延迟，从200ms改为100ms，提高响应性
    }
  }, [messages, shouldAutoScroll, isUserScrolling])

  // 🔥 思考过程展开时的滚动 - 只在用户主动展开时滚动
  const [lastExpandedThought, setLastExpandedThought] = useState<string | null>(null)
  
  useEffect(() => {
    if (!shouldAutoScroll) return

    const expandedThoughts = messages.filter(msg =>
      msg.thoughts && msg.thoughts.length > 0 && !msg.thoughtsCollapsed
    )

    const currentExpanded = expandedThoughts[expandedThoughts.length - 1]?.id
    
    // 只有当有新的思考过程被展开时才滚动
    if (currentExpanded && currentExpanded !== lastExpandedThought) {
      setLastExpandedThought(currentExpanded)
      
      const scrollToBottom = () => {
        requestAnimationFrame(() => {
          messagesEndRef.current?.scrollIntoView({
            behavior: 'smooth',
            block: 'end'
          })
        })
      }

      const timeoutId = setTimeout(scrollToBottom, 200)
      return () => clearTimeout(timeoutId)
    }
  }, [messages.map(m => ({
    id: m.id,
    thoughtsCollapsed: m.thoughtsCollapsed,
    thoughtsLength: m.thoughts?.length || 0
  })), shouldAutoScroll, lastExpandedThought])

  // 🔥 页面初始化时自动滚动到底部 - 修复滚动跳动问题
  useEffect(() => {
    console.log('📱 健康咨询页面初始化，自动滚动到底部')
    
    // 🔥 修复：只有当有消息时才进行初始滚动，避免空页面的滚动跳动
    if (messages.length > 0) {
      // 延迟执行确保DOM已渲染，但减少延迟避免用户感知到跳动
      const timer = setTimeout(() => {
        const element = messagesEndRef.current
        if (element) {
          // 🔥 修复：使用无动画的瞬时滚动，避免用户看到滚动过程
          element.scrollIntoView({ behavior: 'auto', block: 'end' })
          // 🔥 修复：额外确保滚动到最底部
          setTimeout(() => {
            const container = element.parentElement
            if (container) {
              container.scrollTop = container.scrollHeight
            }
          }, 50) // 减少延迟
        }
        setShouldAutoScroll(true)
      }, 100) // 减少延迟，从300ms改为100ms
      
      return () => clearTimeout(timer)
    } else {
      // 无消息时直接启用自动滚动，不进行滚动操作
      setShouldAutoScroll(true)
    }
  }, [messages.length]) // 🔥 修复：依赖messages.length而不是整个messages数组，避免频繁触发

  // 🔥 页面加载时设置body的data-page属性，控制overflow样式
  useEffect(() => {
    console.log('🔧 设置健康咨询页面body样式')
    document.body.setAttribute('data-page', 'consultation')
    
    // 🔥 监听Layout组件的清空对话事件
    const handleClearConversation = () => {
      console.log('🔄 收到清空对话事件')
      clearConversation()
    }
    
    window.addEventListener('clearConversation', handleClearConversation)
    
    return () => {
      console.log('🔧 清除健康咨询页面body样式')
      document.body.removeAttribute('data-page')
      window.removeEventListener('clearConversation', handleClearConversation)
    }
  }, [])

  // 加载应用参数函数
  const loadAppParameters = async () => {
    try {
      console.log('💬 开始加载应用参数')
      
      if (configUtils.isAgentConfigured('doctor')) {
        const params = await difyClient.getAppParameters(config.dify.doctorAgent.appId)
        setAppParameters(params)
        console.log('📋 应用参数加载成功:', params)
      }

      // 尝试从本地存储恢复对话历史
      try {
        const savedData = storageUtils.local.get('health_consultation_history') as HealthConsultationData | null
        if (savedData && savedData.messages && Array.isArray(savedData.messages)) {
          console.log('📚 恢复本地对话历史，消息数量:', savedData.messages.length)

          // 🔥 修复历史消息状态：确保所有历史消息都标记为已完成
          const normalizedMessages = savedData.messages.map(msg => ({
            ...msg,
            // 对于助手消息，确保状态正确
            ...(msg.role === 'assistant' ? {
              loading: false,           // 历史消息不应该显示loading
              isStreaming: false,       // 历史消息不应该显示streaming
              hasStartedContent: Boolean(msg.content && msg.content.trim()), // 只有有内容才标记为已开始
              thinkingCompleted: true,  // 思考过程已完成
              thoughtsCollapsed: true,  // 默认折叠思考过程
              // 确保内容不为空，如果为空则提供默认内容
              content: msg.content && msg.content.trim() ? msg.content : '正在思考...',
            } : {}),
            // 确保所有消息都有基本字段
            timestamp: msg.timestamp || new Date().toISOString(),
          }))

          setMessages(normalizedMessages)
          console.log('✅ 历史消息状态已标准化，助手消息数量:', normalizedMessages.filter(m => m.role === 'assistant').length)

          // 恢复conversationId（如果存在）
          if (savedData.conversationId) {
            console.log('🔗 恢复对话ID:', savedData.conversationId)
            setConversationId(savedData.conversationId)
          }
          
          // 🔥 恢复userId（如果存在）
          if (savedData.userId) {
            console.log('👤 恢复用户ID:', savedData.userId)
            setUserId(savedData.userId)
            userIdRef.current = savedData.userId // 🔥 同时更新ref
          }
          
          // 🔥 恢复历史数据后自动滚动到底部
          setTimeout(() => {
            const element = messagesEndRef.current
            if (element) {
              element.scrollIntoView({ behavior: 'smooth', block: 'end' })
              // 🔥 修复：确保滚动到最底部
              setTimeout(() => {
                const container = element.parentElement
                if (container) {
                  container.scrollTop = container.scrollHeight
                }
              }, 200)
            }
            setShouldAutoScroll(true)
          }, 500)
          
          return
        }
      } catch (error) {
        console.error('❌ 恢复对话历史失败:', error)
      }
      
    } catch (error) {
      console.error('❌ 加载应用参数失败:', error)
    }
  }

  // 加载应用参数
  useEffect(() => {
    loadAppParameters()
    
    // 聚焦输入框
    const focusTimer = setTimeout(() => {
      inputRef.current?.focus()
    }, 1000)
    
    return () => clearTimeout(focusTimer)
  }, []) // 🔥 移除 appParameters 依赖项，避免死循环

  // 单独处理应用参数加载完成后的欢迎消息
  useEffect(() => {
    if (appParameters && messages.length === 0) {
      console.log('📋 应用参数已加载，设置欢迎消息')
      
      // 🔥 使用配置的开场白，如果没有则使用默认开场白
      const openingStatement = appParameters.opening_statement || `默认欢迎消息...`

      // 🔥 只有配置了建议问题才显示，没有配置则不显示
      const suggestedQuestions = (appParameters.suggested_questions && appParameters.suggested_questions.length > 0)
        ? appParameters.suggested_questions 
        : []

      const welcomeMessage: ChatMessage = {
        id: `welcome_${Date.now()}`,
        role: 'assistant',
        content: openingStatement,
        timestamp: new Date().toISOString(),
        suggestedQuestions: suggestedQuestions,
      }
      
      setMessages([welcomeMessage])
      setShowSuggestedQuestions(true)
    }
  }, [appParameters, messages.length]) // 🔥 正确的依赖项：只在应用参数首次加载且消息为空时执行

  // 🔥 自动清除错误消息
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        setError('')
      }, 5000) // 5秒后自动清除错误
      
      return () => clearTimeout(timer)
    }
  }, [error])

  // 发送消息
  const sendMessage = async () => {
    if ((!inputValue.trim() && selectedImages.length === 0) || loading) return

    // 🔥 清除之前的错误信息
    setError('')

    // 检查配置
    if (!configUtils.isAgentConfigured('doctor')) {
      setError('健康咨询服务未配置，请联系管理员')
      return
    }

    // 检查API Key配置
    if (config.dify.apiKey === 'app-test-key-123') {
      setError('请配置正确的Dify API Key，当前使用的是测试密钥')
      console.error('❌ 使用测试API Key，无法连接真实的Dify服务')
      return
    }

    const message = inputValue.trim() || '请基于这些图片分析我的图片结果和健康状况'
    const imageFiles = [...selectedImages]
    const userMessageId = `user_${Date.now()}`
    const assistantMessageId = `assistant_${Date.now()}`

    // 🔥 确保有用户ID，如果没有则生成一个新的，并在获得conversationId后建立映射
    let currentUserId = userId
    if (!currentUserId) {
      // 先生成一个临时的用户ID
      currentUserId = difyClient.getUserId()
      setUserId(currentUserId)
      userIdRef.current = currentUserId // 🔥 同时更新ref
      console.log('👤 生成新的用户ID:', currentUserId)
    } else {
      console.log('👤 使用现有用户ID:', currentUserId)
    }

    // 创建用户消息
    const userMessage: ChatMessage = {
      id: userMessageId,
      role: 'user',
      content: message,
      timestamp: new Date().toISOString(),
      images: imageFiles.length > 0 ? imageFiles : undefined,
    }

    // 创建助手消息（初始状态）
    const assistantMessage: ChatMessage = {
      id: assistantMessageId,
      role: 'assistant',
      content: '',
      timestamp: new Date().toISOString(),
      loading: true,
      isStreaming: true,
      thoughts: [],
      thoughtsCollapsed: true,
      thinkingCompleted: false,
      hasStartedContent: false,
    }

    // 清空输入
    setInputValue('')
    setSelectedImages([])
    setError('')
    setLoading(true)
    setShowSuggestedQuestions(false)

    // 添加消息到界面
    setMessages(prev => [...prev, userMessage, assistantMessage])

    // 🔥 发送新消息时启用自动滚动
    setShouldAutoScroll(true)

    try {
      // 滚动到底部
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
      }, 100)

      // 准备回调函数
      const callbacks = {
        onMessage: (content: string, isComplete: boolean) => {
          console.log('📝 收到消息内容:', { content: content.substring(0, 100), isComplete, contentLength: content.length })
          
          setMessages(prev => prev.map(msg => {
            if (msg.id === assistantMessageId) {
              const updatedMessage = { ...msg }
              
              // 全量更新：替换内容（已经过think标签清理）
              updatedMessage.content = content
              updatedMessage.hasStartedContent = Boolean(content)
              
              if (isComplete) {
                updatedMessage.loading = false
                updatedMessage.isStreaming = false
                updatedMessage.thinkingCompleted = true
              }
              
              console.log('🔄 更新助手消息状态:', {
                contentLength: updatedMessage.content?.length,
                hasStartedContent: updatedMessage.hasStartedContent,
                loading: updatedMessage.loading,
                isStreaming: updatedMessage.isStreaming
              })
              
              return updatedMessage
            }
            return msg
          }))
        },

        onThought: (thought: AgentThought) => {
          console.log('🧠 收到思考过程:', thought)
          
          // 🔥 修复：检查思考是否完成
          const isThinkingCompleted = thought.thought.includes('###THINKING_COMPLETED###')
          
          setMessages(prev => prev.map(msg => {
            if (msg.id === assistantMessageId) {
              const existingThoughts = msg.thoughts || []
              const thoughtExists = existingThoughts.some(t => t.id === thought.id)
              
              // 🔥 修复：清理思考内容中的完成标记
              const cleanedThought = {
                ...thought,
                thought: thought.thought.replace(/###THINKING_COMPLETED###/g, '').trim()
              }
              
              if (!thoughtExists) {
                return {
                  ...msg,
                  thoughts: [...existingThoughts, cleanedThought],
                  thoughtsCollapsed: true,
                  // 🔥 修复：如果思考完成，更新完成状态
                  thinkingCompleted: isThinkingCompleted || msg.thinkingCompleted,
                }
              } else {
                // 更新现有思考
                return {
                  ...msg,
                  thoughts: existingThoughts.map(t => t.id === thought.id ? cleanedThought : t),
                  // 🔥 修复：如果思考完成，更新完成状态
                  thinkingCompleted: isThinkingCompleted || msg.thinkingCompleted,
                }
              }
            }
            return msg
          }))
        },

        onComplete: (response: DifyResponse) => {
          console.log('✅ 流式响应完成:', response)
          
          // 🔥 检查是否有知识库引用
          const extendedResponse = response as any
          if (extendedResponse.citations && extendedResponse.citations.length > 0) {
            console.log('📚 检测到知识库引用:', extendedResponse.citations.length, '条')
          }
          
          // 更新conversationId
          if (response.conversation_id && response.conversation_id !== conversationId) {
            console.log('🔗 更新对话ID:', response.conversation_id)
            setConversationId(response.conversation_id)
          }

          // 标记消息完成
          setMessages(prev => {
            const updatedMessages = prev.map(msg => {
              if (msg.id === assistantMessageId) {
                return {
                  ...msg,
                  // 🔥 保存Dify API返回的真实message_id，用于反馈功能
                  difyMessageId: response.message_id, // 新增字段保存真实ID
                  loading: false,
                  isStreaming: false,
                  thinkingCompleted: true,
                  hasStartedContent: Boolean(msg.content),
                  // 🔥 添加知识库引用信息
                  citations: extendedResponse.citations || [],
                  // 从响应中提取建议问题（如果有的话）
                  suggestedQuestions: extendedResponse.suggested_questions || [],
                }
              }
              return msg
            })

            // 🔥 当获得conversationId后，建立用户映射关系
            if (response.conversation_id && currentUserId) {
              // 确保用户ID与对话ID的映射关系正确建立
              const finalUserId = difyClient.getUserId(response.conversation_id)
              if (finalUserId !== currentUserId) {
                console.log('🔄 更新用户ID映射:', { old: currentUserId, new: finalUserId, conversationId: response.conversation_id })
                setUserId(finalUserId)
                currentUserId = finalUserId
              }
            }

            // 保存对话历史到本地
            const conversationData: HealthConsultationData = {
              messages: updatedMessages,
              conversationId: response.conversation_id || conversationId,
              userId: currentUserId, // 🔥 使用最终确定的userId
              timestamp: Date.now(),
            }

            storageUtils.local.set('health_consultation_history', conversationData)
            console.log('💾 本地对话历史已保存，消息数量:', updatedMessages.length)

            return updatedMessages
          })

          // 如果有建议问题，显示它们
          if (extendedResponse.suggested_questions && extendedResponse.suggested_questions.length > 0) {
            setShowSuggestedQuestions(true)
          }

          setLoading(false)
        },

        onError: (error: Error) => {
          console.error('❌ 流式响应错误:', error)
          setError(error.message || '发送消息失败，请重试')
          setMessages(prev => prev.map(msg => {
            if (msg.id === assistantMessageId) {
              return {
                ...msg,
                loading: false,
                isStreaming: false,
                content: '抱歉，我暂时无法回应。请稍后重试。',
                hasStartedContent: true,
              }
            }
            return msg
          }))
          setLoading(false)
        }
      }

      // 发送消息（根据是否有图片选择不同的方法）
      if (imageFiles.length > 0) {
        console.log('📸 发送包含图片的消息')
        await difyClient.sendMessageWithImages(
          config.dify.doctorAgent.appId,
          message,
          imageFiles,
          callbacks,
          conversationId || undefined
        )
      } else {
        console.log('💬 发送普通文本消息')
        await difyClient.sendMessageStream(
          config.dify.doctorAgent.appId,
          message,
          callbacks,
          conversationId || undefined
        )
      }

    } catch (error) {
      console.error('🚨 发送消息失败:', error)
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      setError(`发送失败: ${errorMessage}`)
      
      // 移除失败的助手消息
      setMessages(prev => prev.filter(msg => msg.id !== assistantMessageId))
      setLoading(false)
    }
  }

  // 处理键盘事件
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      if (e.shiftKey) {
        // Shift + Enter 换行
        return
      }
      
      // Enter 发送消息
      e.preventDefault()
      
      // 检查是否可以发送（有文本输入或有选择的图片）
      if ((inputValue.trim() || selectedImages.length > 0) && !loading) {
        sendMessage()
      }
    }
  }

  // 切换思考过程的折叠状态 - 使用 useCallback 避免重新创建
  const toggleThoughts = useCallback((messageId: string) => {
    setMessages(prevMessages => {
      const updatedMessages = prevMessages.map(msg => {
        if (msg.id === messageId) {
          const newCollapsedState = !msg.thoughtsCollapsed
          // 🔥 如果是展开思考过程，启用自动滚动
          if (!newCollapsedState) {
            setShouldAutoScroll(true)
          }
          return { ...msg, thoughtsCollapsed: newCollapsedState }
        }
        return msg
      })
      return updatedMessages
    })
  }, [])

  // 复制消息 - 使用 useCallback
  const copyMessage = useCallback(async (messageId: string, content: string) => {
    try {
      await navigator.clipboard.writeText(content)
      setCopiedMessageId(messageId)
      setTimeout(() => setCopiedMessageId(null), 2000)
    } catch (error) {
      console.error('复制失败:', error)
    }
  }, [])

  // 清空对话
  const clearConversation = () => {
    // 清理本地状态
    setMessages([])
    setConversationId('')
    storageUtils.local.remove('health_consultation_history')

    // 清理Dify客户端中的用户会话映射
    difyClient.clearUserSession()

    console.log('🗑️ 对话记录已清空，对话ID已重置，用户会话映射已清理')

    // 重新添加欢迎消息
    setTimeout(() => {
      const welcomeMessage: ChatMessage = {
        id: `welcome_${Date.now()}`,
        role: 'assistant',
        content: '对话已重置，请问有什么健康问题需要咨询吗？',
        timestamp: new Date().toISOString(),
      }
      setMessages([welcomeMessage])
    }, 100)
  }

  // 重试最后一条消息
  const retryLastMessage = () => {
    const lastUserMessage = [...messages].reverse().find(msg => msg.role === 'user')
    if (lastUserMessage) {
      setInputValue(lastUserMessage.content)
    }
  }

  // 清除对话历史
  const clearHistory = () => {
    setMessages([])
    setConversationId('')
    storageUtils.local.remove('health_consultation_history')
    console.log('🗑️ 对话历史已清除')
  }

  // 思考过程组件 - 使用 React.memo 避免不必要的重新渲染
  const ThoughtProcess = React.memo(({ message }: { message: ChatMessage }) => {
    if (!message.thoughts || message.thoughts.length === 0) return null

    const mainThought = message.thoughts[0] // 只显示主要的思考过程
    const isStreaming = message.isStreaming

    return (
      <div className="mt-3 border-t border-purple-100">
        <button
          onClick={() => toggleThoughts(message.id)}
          className="flex items-center gap-2 w-full py-2 text-sm text-purple-600 hover:text-purple-800 transition-colors"
        >
          <Brain className="w-4 h-4" />
          <span>AI思考过程</span>
          {isStreaming && (
            <div className="flex items-center space-x-1 ml-2">
              <div className="w-1 h-1 bg-purple-500 rounded-full animate-bounce" />
              <div className="w-1 h-1 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
              <div className="w-1 h-1 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
            </div>
          )}
          {message.thoughtsCollapsed ? (
            <ChevronDown className="w-4 h-4 ml-auto" />
          ) : (
            <ChevronUp className="w-4 h-4 ml-auto" />
          )}
        </button>

        <AnimatePresence mode="wait">
          {!message.thoughtsCollapsed && (
            <motion.div
              key={`thought-${message.id}`}
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="overflow-hidden"
            >
              <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-4 mt-2">
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="relative"
                >
                  {/* 🔥 修改：简化布局，去掉左侧Brain图标，让内容区域更宽 */}
                  <div className="w-full">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-sm font-medium text-purple-700">
                        {mainThought.tool}
                      </span>
                      {isStreaming && !message.thinkingCompleted && (
                        <span className="text-xs text-purple-500 bg-purple-100 px-2 py-1 rounded-full">
                          思考中...
                        </span>
                      )}
                    </div>
                    <div className="text-sm text-gray-700 whitespace-pre-wrap break-words leading-relaxed">
                      {/* 🔥 实时清理所有可能的think标签格式和完成标记 */}
                      {(() => {
                        const thoughtContent = mainThought.thought || '正在思考...'
                        // 清理所有可能的think标签格式和特殊标记
                        const cleanedContent = thoughtContent
                          .replace(/^<think>/gi, '')                    // 开头的<think>
                          .replace(/<\/think>$/gi, '')                  // 结尾的</think>
                          .replace(/<\/?think>/gi, '')                  // 中间的任何think标签
                          .replace(/###THINKING_COMPLETED###/g, '')     // 🔥 修复：移除完成标记
                          .trim()
                        
                        return cleanedContent || '正在思考...'
                      })()}
                      {/* 🔥 修复：流式思考时的光标效果 - 只有在真正思考中且未完成时才显示 */}
                      {isStreaming && !message.thinkingCompleted && mainThought.thought && !mainThought.thought.includes('###THINKING_COMPLETED###') && (
                        <motion.span
                          animate={{ opacity: [1, 0, 1] }}
                          transition={{ duration: 1, repeat: Infinity }}
                          className="inline-block w-1 h-4 bg-purple-500 ml-1"
                        />
                      )}
                    </div>
                  </div>
                </motion.div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    )
  }, (prevProps, nextProps) => {
    // 自定义比较函数，只在关键属性变化时才重新渲染
    const prev = prevProps.message
    const next = nextProps.message

    return (
      prev.id === next.id &&
      prev.content === next.content &&
      prev.thoughtsCollapsed === next.thoughtsCollapsed &&
      prev.isStreaming === next.isStreaming &&
      prev.thoughts?.length === next.thoughts?.length &&
      prev.thoughts?.[0]?.thought === next.thoughts?.[0]?.thought
    )
  })

  // 渲染消息 - 使用 useCallback 避免重新创建
  const renderMessage = useCallback((message: ChatMessage) => {
    const isUser = message.role === 'user'
    const isStreaming = message.isStreaming
    const hasThoughts = message.thoughts && message.thoughts.length > 0

    // 🔥 定义辅助组件
    const LoadingIndicator = ({ text }: { text: string }) => (
      <div className="flex items-center space-x-2 text-purple-600">
        <Loader2 className="w-4 h-4 animate-spin" />
        <span className="text-sm font-medium">{text}</span>
      </div>
    )

    const MessageContent = ({ content }: { content: string }) => (
      <div className={`text-xs sm:text-sm leading-relaxed ${isUser ? 'text-white' : 'text-gray-800'}`}>
        {/* 🎨 用户消息直接显示，助手消息渲染Markdown */}
        {isUser ? (
          <div className="whitespace-pre-wrap">
            {content}
          </div>
        ) : (
          <div className="prose prose-sm max-w-none prose-gray break-words">
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              components={{
                // 自定义组件样式 - 健康咨询专业风格
                h1: ({ children }) => (
                  <h1 className="text-base font-bold text-gray-900 mb-3 pb-2 border-b border-purple-200">
                    {children}
                  </h1>
                ),
                h2: ({ children }) => (
                  <h2 className="text-sm font-semibold text-purple-800 mb-2 mt-4">
                    {children}
                  </h2>
                ),
                h3: ({ children }) => (
                  <h3 className="text-sm font-medium text-purple-700 mb-2 mt-3">
                    {children}
                  </h3>
                ),
                p: ({ children }) => (
                  <p className="mb-3 last:mb-0 text-gray-800 leading-relaxed">
                    {children}
                  </p>
                ),
                strong: ({ children }) => (
                  <strong className="font-semibold text-purple-900">
                    {children}
                  </strong>
                ),
                em: ({ children }) => (
                  <em className="italic text-purple-700">
                    {children}
                  </em>
                ),
                code: ({ children }) => (
                  <code className="text-purple-700 bg-purple-50 px-1.5 py-0.5 rounded text-xs font-mono">
                    {children}
                  </code>
                ),
                ul: ({ children }) => (
                  <ul className="list-disc mb-3 space-y-0.5 ml-5 pl-1">
                    {children}
                  </ul>
                ),
                ol: ({ children }) => (
                  <ol className="list-decimal mb-3 space-y-0.5 ml-5 pl-1">
                    {children}
                  </ol>
                ),
                li: ({ children }) => (
                  <li className="text-gray-800 leading-normal">
                    {children}
                  </li>
                ),
                blockquote: ({ children }) => (
                  <blockquote className="border-l-4 border-purple-300 pl-4 py-2 italic text-purple-800 bg-purple-50 rounded-r mb-3">
                    {children}
                  </blockquote>
                ),
                hr: () => (
                  <hr className="border-purple-200 my-4" />
                ),
                // 🔥 链接样式 - 防止超出容器
                a: ({ children, href }) => (
                  <a 
                    href={href} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-purple-600 hover:text-purple-800 underline break-all max-w-full inline-block"
                    style={{ wordBreak: 'break-all', overflowWrap: 'anywhere' }}
                  >
                    {children}
                  </a>
                ),
                // 表格样式（如果有的话）
                table: ({ children }) => (
                  <div className="overflow-x-auto mb-3">
                    <table className="min-w-full border border-purple-200 rounded">
                      {children}
                    </table>
                  </div>
                ),
                th: ({ children }) => (
                  <th className="border border-purple-200 px-3 py-2 bg-purple-50 text-purple-800 font-semibold text-left">
                    {children}
                  </th>
                ),
                td: ({ children }) => (
                  <td className="border border-purple-200 px-3 py-2 text-gray-800">
                    {children}
                  </td>
                ),
              }}
            >
              {content}
            </ReactMarkdown>
          </div>
        )}
        {/* 🎨 优雅的流式光标效果 - 使用紫色主题，参考思考过程的设计 */}
        {isStreaming && !isUser && (
          <motion.span
            animate={{ opacity: [1, 0, 1] }}
            transition={{ duration: 1, repeat: Infinity }}
            className="inline-block w-1 h-4 bg-purple-500 ml-1"
          />
        )}
      </div>
    )

    const renderMessageContent = () => {
      // 🔥 优先检查消息是否有内容
      if (message.content && message.content.trim()) {
        return <MessageContent content={message.content} />
      }
      // 然后才检查流式状态
      if (hasThoughts && !message.hasStartedContent) {
        return <LoadingIndicator text="正在思考中..." />
      }
      if (message.hasStartedContent) {
        return <MessageContent content={message.content || ''} />
      }
      return <LoadingIndicator text="正在处理中..." />
    }

    return (
      <motion.div
        key={message.id}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-3`}
      >
        <div className={`flex max-w-[90%] sm:max-w-[85%] lg:max-w-[75%] ${isUser ? 'flex-row-reverse' : 'flex-row'}`}>
          {/* 头像 - 响应式尺寸 */}
          <div
            className={`flex-shrink-0 w-7 h-7 sm:w-8 sm:h-8 rounded-full flex items-center justify-center ${isUser ? 'bg-blue-500 ml-2' : isStreaming ? 'bg-purple-400 mr-2 animate-pulse' : 'bg-purple-500 mr-2'
              }`}
          >
            {isUser ? (
              <User className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
            ) : (
              <Bot className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
            )}
          </div>

          {/* 消息内容 - 响应式设计 */}
          <div className="flex flex-col space-y-1 max-w-full">
            {/* AI思考过程 - 只对助手消息显示 */}
            {!isUser && hasThoughts && (
              <ThoughtProcess message={message} />
            )}

            {/* 主要消息内容 */}
            <div
              className={`rounded-2xl px-3 py-2 sm:px-4 sm:py-3 ${isUser
                ? 'bg-blue-500 text-white rounded-br-sm'
                : isStreaming
                  ? 'bg-gradient-to-br from-purple-50 to-indigo-50 border border-purple-200 text-gray-800 rounded-bl-sm'
                  : 'bg-white border border-gray-200 text-gray-800 rounded-bl-sm'
                }`}
            >
              {renderMessageContent()}

              {/* 消息时间和操作 */}
              <div
                className={`flex items-center justify-between mt-2 pt-2 border-t ${isUser ? 'border-blue-400/30' : 'border-gray-200'
                  }`}
              >
                <div className="flex items-center space-x-2">
                  <span className={`text-xs ${isUser ? 'text-blue-100' : 'text-gray-500'}`}>
                    {/* 时分秒 */ }
                    {dateUtils.format(message.timestamp || Date.now(), 'HH:mm:ss')}
                  </span>
                </div>

                {/* 🔥 助手消息的操作按钮 */}
                {!isUser && !message.loading && message.content && message.difyMessageId && (
                  <div className="flex items-center space-x-2">
                    {/* 复制按钮 */}
                    <button
                      onClick={() => copyMessage(message.id, message.content || '')}
                      className="text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      {copiedMessageId === message.id ? (
                        <Check className="w-4 h-4" />
                      ) : (
                        <Copy className="w-4 h-4" />
                      )}
                    </button>

                    {/* 反馈按钮 */}
                    <div className="flex items-center space-x-1">
                      <button
                        onClick={() => sendFeedback(message.id, 'like')}
                        className={`p-1 rounded transition-colors ${
                          message.feedback?.rating === 'like'
                            ? 'text-green-600 bg-green-50'
                            : 'text-gray-400 hover:text-green-600 hover:bg-green-50'
                        }`}
                        title="有帮助"
                      >
                        <ThumbsUp className="w-3 h-3" />
                      </button>
                      <button
                        onClick={() => sendFeedback(message.id, 'dislike')}
                        className={`p-1 rounded transition-colors ${
                          message.feedback?.rating === 'dislike'
                            ? 'text-red-600 bg-red-50'
                            : 'text-gray-400 hover:text-red-600 hover:bg-red-50'
                        }`}
                        title="没有帮助"
                      >
                        <ThumbsDown className="w-3 h-3" />
                      </button>
                    </div>
                  </div>
                )}

                {/* 🔥 用户消息的图片显示 */}
                {isUser && message.images && message.images.length > 0 && (
                  <div className="text-xs text-blue-100">
                    📷 {message.images.length} 张图片
                  </div>
                )}
              </div>

              {/* 🔥 RAG知识库引用 */}
              {!isUser && message.citations && message.citations.length > 0 && (
                <div className="mt-3 pt-3 border-t border-gray-200">
                  <div className="text-xs text-gray-600 mb-2">
                    📚 引用知识库:
                  </div>
                  {message.citations.map((citation, index) => (
                    <div
                      key={index}
                      className="text-xs bg-gray-50 border border-gray-200 rounded p-2 mb-2 last:mb-0"
                    >
                      <div className="font-medium text-gray-800 mb-1">
                        {citation.title}
                      </div>
                      <div className="text-gray-600 leading-relaxed">
                        {citation.content.substring(0, 150)}
                        {citation.content.length > 150 && '...'}
                      </div>
                      {citation.source && (
                        <div className="text-gray-500 mt-1">
                          来源: {citation.source}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}


            </div>
          </div>
        </div>
      </motion.div>
    )
  }, [copiedMessageId, copyMessage])

  // 发送消息反馈（点赞/踩）- 使用useCallback避免闭包问题
  const sendFeedback = useCallback(async (messageId: string, rating: 'like' | 'dislike') => {
    // 🔥 使用ref获取最新的userId值，避免闭包问题
    const currentUserId = userIdRef.current
    console.log('👍👎 发送消息反馈 userId: ', currentUserId)
    
    try {
      console.log(`👍👎 发送消息反馈: ${messageId} -> ${rating}`)
      
      // 🔥 使用函数式更新获取最新的messages状态
      setMessages(currentMessages => {
        console.log(`🔍 当前messages数量: ${currentMessages.length}`)
        
        // 查找对应的消息，获取真实的Dify message_id
        const targetMessage = currentMessages.find(msg => msg.id === messageId)
        if (!targetMessage) {
          console.error('❌ 找不到对应的消息', messageId)
          setError('无法找到对应的消息')
          return currentMessages // 返回原状态
        }

        if (!targetMessage.difyMessageId) {
          console.error('❌ 消息没有有效的Dify message_id')
          setError('此消息暂时无法评价，请重新发送消息后再试')
          return currentMessages // 返回原状态
        }

        console.log(`🔗 使用真实的Dify message_id: ${targetMessage.difyMessageId}`)
        console.log('🔍 使用最新的用户ID:', currentUserId)
        
        // 🔥 异步调用API，但先更新UI状态提供即时反馈
        difyClient.sendMessageFeedback(targetMessage.difyMessageId, { rating }, currentUserId)
          .then(() => {
            console.log('✅ 反馈发送成功')
          })
          .catch((error) => {
            console.error('❌ 发送反馈失败:', error)
            setError('发送反馈失败，请稍后重试')
            // 失败时回滚UI状态
            setMessages(prevMessages => prevMessages.map(msg => 
              msg.id === messageId 
                ? { ...msg, feedback: undefined }
                : msg
            ))
          })
        
        // 🔥 立即更新UI状态提供用户反馈
        return currentMessages.map(msg => 
          msg.id === messageId 
            ? { ...msg, feedback: { rating } }
            : msg
        )
      })
      
    } catch (error) {
      console.error('❌ 发送反馈失败:', error)
      setError('发送反馈失败，请稍后重试')
    }
  }, []) // 🔥 移除所有依赖，在函数内部动态获取最新值

  // 🔥 图片处理相关函数
  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    if (files.length === 0) return

    console.log('📷 用户选择图片:', files.map(f => ({ name: f.name, size: f.size, type: f.type })))

    // 验证图片
    const validImages = files.filter(file => {
      // 检查文件类型
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']
      if (!allowedTypes.includes(file.type)) {
        console.warn('❌ 不支持的图片格式:', file.type)
        return false
      }

      // 检查文件大小 (10MB限制)
      const maxSize = 10 * 1024 * 1024
      if (file.size > maxSize) {
        console.warn('❌ 图片文件过大:', file.size)
        return false
      }

      return true
    })

    if (validImages.length !== files.length) {
      setError('部分图片格式不支持或文件过大，仅支持 JPG、PNG、GIF、WEBP、SVG 格式，且单个文件不超过10MB')
    }

    // 限制最多5张图片
    const maxImages = 5
    const currentTotal = selectedImages.length + validImages.length
    const imagesToAdd = currentTotal > maxImages 
      ? validImages.slice(0, maxImages - selectedImages.length)
      : validImages

    if (currentTotal > maxImages) {
      setError(`最多只能上传${maxImages}张图片`)
    }

    setSelectedImages(prev => [...prev, ...imagesToAdd])
    
    // 清空input
    if (event.target) {
      event.target.value = ''
    }
  }

  const removeImage = (index: number) => {
    console.log('🗑️ 删除图片:', index)
    setSelectedImages(prev => prev.filter((_, i) => i !== index))
  }

  const handleSuggestedQuestionClick = (question: string) => {
    console.log('💡 用户点击建议问题:', question)
    setInputValue(question)
    setShowSuggestedQuestions(false)
    // 聚焦输入框
    setTimeout(() => {
      inputRef.current?.focus()
    }, 100)
  }

  // 🔥 获取当前可显示的建议问题
  const getCurrentSuggestedQuestions = () => {
    // 优先显示最新助手消息的建议问题
    const lastAssistantMessage = [...messages].reverse().find(msg => 
      msg.role === 'assistant' && 
      msg.suggestedQuestions && 
      msg.suggestedQuestions.length > 0
    )
    
    if (lastAssistantMessage?.suggestedQuestions) {
      return lastAssistantMessage.suggestedQuestions
    }
    
    // 如果没有消息中的建议问题，使用应用参数中的建议问题
    if (appParameters?.suggested_questions && appParameters.suggested_questions.length > 0) {
      return appParameters.suggested_questions
    }
    
    return []
  }

  // 🔥 同步userId到ref，避免闭包问题
  useEffect(() => {
    userIdRef.current = userId
    console.log('🔄 更新用户ID到ref:', userId)
  }, [userId])

  // 🔥 数据持久化 - 当消息或对话状态变化时自动保存
  useEffect(() => {
    if (messages.length > 0) {
      console.log('💾 保存对话历史到本地存储，消息数量:', messages.length)
      
      const dataToSave: HealthConsultationData = {
        messages,
        conversationId: conversationId || undefined,
        userId: userId || undefined,
        timestamp: Date.now(),
      }
      
      // 🔥 确保保存的数据结构正确
      const validMessages = messages.filter(msg => 
        msg.id && 
        msg.role && 
        msg.content !== undefined && 
        msg.timestamp
      )
      
      if (validMessages.length > 0) {
        storageUtils.local.set('health_consultation_history', {
          ...dataToSave,
          messages: validMessages
        })
        console.log('✅ 对话历史已保存，有效消息数量:', validMessages.length)
      }
    }
  }, [messages, conversationId, userId])

  return (
    <div className="flex flex-col bg-gray-50 min-h-0 flex-1">
      {/* 🔥 消息列表 - 🔥 确保不阻挡Layout组件的免责声明 */}
      <div 
        className="flex-1 overflow-y-auto space-y-3 px-3 sm:px-4 hide-scrollbar relative"
        style={{ 
          paddingTop: '20px',
          paddingBottom: '130px',
        }}
      >
        <AnimatePresence>{messages.map(renderMessage)}</AnimatePresence>
        {/* 🔥 修复：确保ref元素有足够的高度，便于滚动定位 */}
        <div ref={messagesEndRef} className="h-4" />
        
        {/* 🔥 滚动到底部按钮 - 当用户向上滚动时显示 */}
        {!shouldAutoScroll && (
          <motion.button
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            onClick={() => {
              setShouldAutoScroll(true)
              const element = messagesEndRef.current
              if (element) {
                element.scrollIntoView({ behavior: 'smooth', block: 'end' })
                // 🔥 修复：确保滚动到最底部
                setTimeout(() => {
                  const container = element.parentElement
                  if (container) {
                    container.scrollTop = container.scrollHeight
                  }
                }, 100)
              }
            }}
            className="fixed bottom-[140px] right-6 z-10 w-12 h-12 bg-purple-500 hover:bg-purple-600 text-white rounded-full shadow-lg flex items-center justify-center transition-all duration-200"
            title="滚动到底部"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
          </motion.button>
        )}
      </div>

      {/* 🔥 全局建议问题 - 固定在输入框上方，全宽显示 */}
      {(() => {
        const currentSuggestedQuestions = getCurrentSuggestedQuestions()
        return showSuggestedQuestions && currentSuggestedQuestions.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            className="fixed bottom-[100px] left-0 right-0 z-10 mx-3 bg-white border border-purple-200 rounded-xl shadow-lg p-3"
            style={{ maxHeight: '200px', overflowY: 'auto' }}
          >
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-purple-700">💡 建议问题</span>
              <button
                onClick={() => setShowSuggestedQuestions(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
            <div className="space-y-2">
              {currentSuggestedQuestions.map((question, index) => (
                <button
                  key={index}
                  onClick={() => handleSuggestedQuestionClick(question)}
                  className="block w-full text-left text-sm bg-purple-50 hover:bg-purple-100 border border-purple-200 rounded-lg px-3 py-2 transition-colors"
                >
                  {question}
                </button>
              ))}
            </div>
          </motion.div>
        )
      })()}

      {/* 错误提示 - 固定在输入框上面，全宽显示 */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="fixed bottom-[100px] left-0 right-0 z-10 mx-3 flex items-center space-x-2 text-red-600 bg-red-50 px-3 py-2 rounded-lg shadow-lg"
        >
          <AlertCircle className="w-4 h-4" />
          <span className="text-xs">{error}</span>
        </motion.div>
      )}

      {/* 输入区域 - 固定在底部，全宽显示 */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-20 p-3 sm:p-4">
        
        {/* 🔥 图片预览区域 */}
        {selectedImages.length > 0 && (
          <div className="mb-3 p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">
                已选择 {selectedImages.length} 张图片
              </span>
              <button
                onClick={() => setSelectedImages([])}
                className="text-xs text-red-600 hover:text-red-800"
              >
                清空所有
              </button>
            </div>
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
              {selectedImages.map((image, index) => (
                <div key={index} className="relative group">
                  <div className="aspect-square bg-gray-200 rounded-lg overflow-hidden">
                    <img
                      src={URL.createObjectURL(image)}
                      alt={`预览 ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <button
                    onClick={() => removeImage(index)}
                    className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center"
                  >
                    <X className="w-3 h-3" />
                  </button>
                  <div className="absolute bottom-0 left-0 right-0 bg-black/60 text-white text-xs p-1 rounded-b-lg truncate">
                    {image.name}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="flex space-x-2 sm:space-x-3">
          {/* 图片选择按钮 */}
          <button
            onClick={() => fileInputRef.current?.click()}
            disabled={loading}
            className="flex-shrink-0 p-2 text-gray-500 hover:text-purple-600 hover:bg-purple-50 rounded-full transition-colors disabled:opacity-50"
            title="添加图片"
          >
            <ImageIcon className="w-5 h-5" />
          </button>

          {/* 隐藏的文件选择input */}
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            multiple
            onChange={handleImageSelect}
            className="hidden"
          />

          <textarea
            ref={inputRef}
            value={inputValue}
            onChange={e => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={selectedImages.length > 0 ? "描述您想了解的问题（可选）..." : "描述您的健康问题..."}
            rows={1}
            className="flex-1 resize-none border-none outline-none bg-transparent text-sm sm:text-base text-gray-900 placeholder-gray-500 py-[10px] leading-[1.5rem]"
            style={{ minHeight: '40px', maxHeight: '80px' }}
            disabled={loading}
          />

          {/* 🔥 修复发送按钮样式为圆形 */}
          <button
            onClick={sendMessage}
            disabled={loading || (!inputValue.trim() && selectedImages.length === 0)}
            className={`flex-shrink-0 w-10 h-10 rounded-full transition-all duration-200 flex items-center justify-center ${
              loading || (!inputValue.trim() && selectedImages.length === 0)
                ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                : 'bg-purple-500 hover:bg-purple-600 text-white shadow-md hover:shadow-lg'
            }`}
          >
            {loading ? (
              <Loader2 className="w-5 h-5 animate-spin" />
            ) : (
              <Send className="w-5 h-5" />
            )}
          </button>
        </div>

        {/* 图片上传提示 */}
        {selectedImages.length === 0 && (
          <div className="mt-2 text-xs text-gray-500">
            支持上传图片，最多5张，单个不超过10MB
          </div>
        )}
      </div>
    </div>
  )
}

export default HealthConsultation