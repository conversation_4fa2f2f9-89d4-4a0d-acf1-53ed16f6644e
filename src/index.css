@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式 */
@layer base {
  html {
    /* 禁用移动端双击缩放 */
    touch-action: manipulation;
    /* 优化滚动性能 */
    -webkit-overflow-scrolling: touch;
  }
  
  body {
    @apply font-sans bg-gray-50 text-gray-900 min-h-screen;
    /* 移动端字体优化 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* 桌面端保持文字选择功能，移动端禁用 */
    -webkit-user-select: auto;
    user-select: auto;
    /* 🔥 默认允许滚动，只在特定页面禁止滚动 */
    overflow: auto;
  }

  /* 🔥 健康咨询页面专用的body样式 - 通过data属性控制 */
  body[data-page="consultation"] {
    /* 移除 overflow: hidden，允许页面正常滚动 */
    overflow: auto;
  }

  /* 移动端特殊处理 */
  @media (max-width: 768px) {
    body {
      -webkit-user-select: none;
      user-select: none;
    }
  }

  /* 移动端输入框样式重置 */
  input, textarea, select {
    @apply outline-none;
    -webkit-appearance: none;
    border-radius: 0;
  }

  /* 移动端按钮样式重置 */
  button {
    -webkit-appearance: none;
    border-radius: 0;
    background: none;
    border: none;
    outline: none;
  }

  /* 隐藏滚动条但保持滚动功能 */
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }
}

/* 组件样式 */
@layer components {
  /* 功能卡片基础样式 - 响应式设计 */
  .feature-card {
    @apply bg-white rounded-2xl shadow-card hover:shadow-card-hover 
           transition-all duration-300 cursor-pointer
           border border-gray-100;
  }

  /* 功能卡片激活状态 - 移动端和桌面端不同处理 */
  .feature-card:active {
    @apply transform scale-95;
  }

  @media (min-width: 768px) {
    .feature-card:hover {
      @apply transform scale-105 shadow-lg;
    }
    
    .feature-card:active {
      @apply transform scale-100;
    }
  }

  /* 图标容器样式 - 响应式尺寸 */
  .icon-container {
    @apply w-12 h-12 sm:w-16 sm:h-16 rounded-full flex items-center justify-center
           shadow-sm transition-transform duration-300;
  }

  /* 渐变背景 */
  .gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  /* 按钮样式 - 响应式设计 */
  .btn-primary {
    @apply bg-primary-600 text-white px-4 py-2 sm:px-6 sm:py-3 rounded-xl 
           font-medium text-sm sm:text-base
           hover:bg-primary-700 focus:ring-4 focus:ring-primary-200
           transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-secondary {
    @apply bg-gray-100 text-gray-700 px-4 py-2 sm:px-6 sm:py-3 rounded-xl 
           font-medium text-sm sm:text-base
           hover:bg-gray-200 focus:ring-4 focus:ring-gray-100
           transition-all duration-200;
  }

  /* 加载动画 */
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }

  /* 文字渐变效果 */
  .text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  /* 聊天界面样式 - 响应式调整 */
  .chat-container {
    @apply max-h-96 sm:max-h-[32rem] lg:max-h-[40rem] overflow-y-auto;
  }

  .chat-message {
    @apply mb-3 sm:mb-4 animate-fade-in;
  }

  .chat-input {
    @apply w-full p-3 sm:p-4 text-sm sm:text-base rounded-xl border border-gray-300 
           focus:ring-2 focus:ring-primary-500 focus:border-transparent
           resize-none;
  }

  /* AI思考过程样式 */
  .thought-container {
    @apply bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-xl p-3 shadow-sm;
  }

  .thought-item {
    @apply bg-white/80 backdrop-blur-sm border border-purple-100 rounded-lg p-2 hover:shadow-sm transition-all duration-200;
  }

  .thought-badge {
    @apply w-5 h-5 bg-gradient-to-br from-purple-100 to-purple-200 rounded-full flex items-center justify-center text-xs font-medium text-purple-700 shadow-sm;
  }

  /* 流式输出光标效果 */
  .streaming-cursor {
    @apply inline-block w-0.5 h-4 bg-green-500 ml-1 animate-pulse;
  }

  /* 流式状态指示器 */
  .streaming-indicator {
    @apply flex items-center space-x-1;
  }

  .streaming-dot {
    @apply w-1 h-1 bg-green-500 rounded-full animate-bounce;
  }
}

/* 动画增强 */
@layer utilities {
  /* 浮动动画 */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  /* 呼吸效果 */
  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* 移动端安全区域适配 */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* 🔥 增强的安全区域适配 - 确保在有底部指示条的手机上正确处理 */
  .safe-area-bottom-enhanced {
    padding-bottom: max(1.5rem, env(safe-area-inset-bottom));
  }

  /* 🔥 移动端专用的底部安全区域 */
  @media (max-width: 768px) {
    .mobile-safe-bottom {
      padding-bottom: max(1rem, env(safe-area-inset-bottom));
    }
  }

  /* 响应式间距工具类 */
  .space-responsive {
    @apply space-y-4 sm:space-y-6 lg:space-y-8;
  }

  .gap-responsive {
    @apply gap-3 sm:gap-4 lg:gap-6;
  }

  /* 响应式文字大小 */
  .text-responsive-sm {
    @apply text-xs sm:text-sm lg:text-base;
  }

  .text-responsive-base {
    @apply text-sm sm:text-base lg:text-lg;
  }

  .text-responsive-lg {
    @apply text-base sm:text-lg lg:text-xl;
  }

  .text-responsive-xl {
    @apply text-lg sm:text-xl lg:text-2xl;
  }

  /* 桌面端优化 */
  @media (min-width: 1024px) {
    .desktop-grid {
      @apply grid-cols-3;
    }
    
    .desktop-wide {
      @apply max-w-6xl;
    }
  }

  /* 打印样式优化 */
  @media print {
    .no-print {
      display: none !important;
    }
    
    .feature-card {
      @apply shadow-none border-2 border-gray-300;
    }
  }
}