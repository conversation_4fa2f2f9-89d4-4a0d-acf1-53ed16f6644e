#!/bin/sh
echo "wrap.sh @: " $@

# 设置npm缓存目录到当前工作目录
export npm_config_cache=/workspace/.npm-cache
mkdir -p /workspace/.npm-cache

[ -e .npmrc ] && rm .npmrc
echo "registry=https://registry.npmmirror.com" >> .npmrc
echo "sass_binary_site=https://npmmirror.com/mirrors/node-sass" >> .npmrc
echo "cache=/workspace/.npm-cache" >> .npmrc
cat .npmrc

# 打印环境变量用于调试
echo "环境变量检查: $1"

# 清理可能存在的平台不匹配的依赖
rm -rf node_modules

# $1: version
npm install --legacy-peer-deps
VITE_APP_VERSION=$1 npm run build
