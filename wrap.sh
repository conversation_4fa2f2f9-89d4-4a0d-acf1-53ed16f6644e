#!/bin/bash

# 健康助理前端Docker包装和部署脚本
# 使用方法: ./wrap.sh [version] [environment] [action]
# 示例: ./wrap.sh 1.0.0 dev build
# 示例: ./wrap.sh 1.0.0 dev deploy
# 示例: ./wrap.sh 1.0.0 dev all

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示使用帮助
show_help() {
    echo "健康助理前端Docker包装和部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 <version> <environment> <action>"
    echo ""
    echo "参数说明:"
    echo "  version     版本号 (如: 1.0.0)"
    echo "  environment 环境 (dev|test|prod)"
    echo "  action      操作 (build|push|deploy|all)"
    echo ""
    echo "操作说明:"
    echo "  build   - 构建应用并创建Docker镜像"
    echo "  push    - 推送Docker镜像到仓库"
    echo "  deploy  - 部署容器到本地"
    echo "  all     - 执行所有操作 (build + push + deploy)"
    echo ""
    echo "示例:"
    echo "  $0 1.0.0 dev build     # 构建开发环境镜像"
    echo "  $0 1.0.0 prod deploy   # 部署生产环境"
    echo "  $0 1.0.0 test all      # 完整流程"
}

# 检查参数
if [ $# -lt 3 ]; then
    log_error "参数不足"
    show_help
    exit 1
fi

VERSION=$1
ENVIRONMENT=$2
ACTION=$3

# 验证参数
if ! [[ $VERSION =~ ^[0-9]+\.[0-9]+\.[0-9]+(-[a-zA-Z0-9]+)?$ ]]; then
    log_error "版本号格式不正确"
    exit 1
fi

if [[ ! "$ENVIRONMENT" =~ ^(dev|test|prod)$ ]]; then
    log_error "环境参数不正确，支持: dev, test, prod"
    exit 1
fi

if [[ ! "$ACTION" =~ ^(build|push|deploy|all)$ ]]; then
    log_error "操作参数不正确，支持: build, push, deploy, all"
    exit 1
fi

# 配置变量
REGISTRY="registry.cn-shanghai.aliyuncs.com"
NAMESPACE="chos"
IMAGE_NAME="health-assist-agent-frontend"
CONTAINER_NAME="health-assist-agent-frontend"

# 根据环境设置不同的配置
case $ENVIRONMENT in
    "dev")
        PORT=8060
        ;;
    "test")
        PORT=8061
        ;;
    "prod")
        PORT=8062
        ;;
esac

# 完整的镜像标签
FULL_IMAGE_TAG="${REGISTRY}/${NAMESPACE}/${IMAGE_NAME}-${ENVIRONMENT}:${VERSION}"
CONTAINER_FULL_NAME="${CONTAINER_NAME}-${ENVIRONMENT}"

log_info "=== 健康助理前端Docker部署 ==="
log_info "版本: $VERSION"
log_info "环境: $ENVIRONMENT"
log_info "操作: $ACTION"
log_info "镜像: $FULL_IMAGE_TAG"
log_info "容器: $CONTAINER_FULL_NAME"
log_info "端口: $PORT"

# 检查Docker
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker服务未启动"
        exit 1
    fi
}

# 构建应用和Docker镜像
build_image() {
    log_info "开始构建流程..."
    
    # 1. 构建前端应用
    log_info "执行前端构建..."
    chmod +x build.sh
    ./build.sh $VERSION $ENVIRONMENT
    
    # 2. 构建Docker镜像
    log_info "构建Docker镜像..."
    docker build \
        --platform linux/amd64 \
        --build-arg VERSION=$VERSION \
        --build-arg ENVIRONMENT=$ENVIRONMENT \
        -f Dockerfile \
        -t $FULL_IMAGE_TAG \
        .
    
    log_success "Docker镜像构建完成: $FULL_IMAGE_TAG"
    
    # 显示镜像信息
    docker images | grep $IMAGE_NAME | head -5
}

# 推送镜像到仓库
push_image() {
    log_info "推送镜像到仓库..."
    
    # 检查镜像是否存在
    if ! docker images | grep -q "$FULL_IMAGE_TAG"; then
        log_error "镜像不存在，请先构建: $FULL_IMAGE_TAG"
        exit 1
    fi
    
    # 推送镜像
    docker push $FULL_IMAGE_TAG
    log_success "镜像推送完成: $FULL_IMAGE_TAG"
}

# 部署容器
deploy_container() {
    log_info "部署容器..."
    
    # 停止并删除旧容器
    log_info "清理旧容器..."
    docker rm -f $CONTAINER_FULL_NAME 2>/dev/null || echo "容器不存在，跳过删除"
    
    # 启动新容器
    log_info "启动新容器..."
    docker run \
        --restart always \
        -d \
        -p $PORT:80 \
        -e TZ=Asia/Shanghai \
        --name $CONTAINER_FULL_NAME \
        $FULL_IMAGE_TAG
    
    # 等待容器启动
    sleep 3
    
    # 检查容器状态
    if docker ps | grep -q $CONTAINER_FULL_NAME; then
        log_success "容器启动成功: $CONTAINER_FULL_NAME"
        log_info "访问地址: http://localhost:$PORT"
        
        # 显示容器信息
        docker ps | grep $CONTAINER_FULL_NAME
        
        # 健康检查
        log_info "执行健康检查..."
        sleep 5
        if curl -f http://localhost:$PORT/health &> /dev/null; then
            log_success "健康检查通过"
        else
            log_warning "健康检查失败，请检查应用状态"
        fi
    else
        log_error "容器启动失败"
        docker logs $CONTAINER_FULL_NAME
        exit 1
    fi
}

# 主执行逻辑
main() {
    check_docker
    
    case $ACTION in
        "build")
            build_image
            ;;
        "push")
            push_image
            ;;
        "deploy")
            deploy_container
            ;;
        "all")
            build_image
            push_image
            deploy_container
            ;;
    esac
    
    log_success "操作完成: $ACTION"
}

# 执行主函数
main

log_success "=== 部署脚本执行完成 ==="
