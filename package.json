{"name": "health-assistant-agent", "version": "1.0.0", "description": "个人健康助理", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx}\"", "lint": "prettier --check \"src/**/*.{ts,tsx,js,jsx}\""}, "dependencies": {"@tailwindcss/typography": "^0.5.16", "axios": "^1.5.0", "dayjs": "^1.11.9", "framer-motion": "^10.16.4", "lucide-react": "^0.276.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^10.1.0", "react-router-dom": "^6.15.0", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1"}, "devDependencies": {"@types/node": "^22.13.14", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.15", "cross-env": "^7.0.3", "postcss": "^8.4.29", "prettier": "^3.5.3", "tailwindcss": "^3.3.3", "typescript": "^5.8.2", "vite": "^4.5.14"}, "engines": {"node": ">=16.0.0"}, "keywords": ["health", "assistant", "react", "vite", "h5", "mobile"]}