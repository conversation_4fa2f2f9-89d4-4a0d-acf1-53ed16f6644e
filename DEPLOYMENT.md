# 健康助理前端部署指南

## 概述

本文档详细说明了健康助理前端应用的部署流程，包括自动化脚本使用、Docker部署和生产环境配置。

## 部署架构

```
┌─────────────────────────────────────────────────────────────┐
│                    阿里云容器镜像服务                          │
│  registry.cn-shanghai.aliyuncs.com/chos/                   │
│  ├── health-assist-agent-frontend-dev:version              │
│  ├── health-assist-agent-frontend-test:version             │
│  └── health-assist-agent-frontend-prod:version             │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      部署环境                                │
│  ├── 开发环境 (dev)  - 端口 8060                            │
│  ├── 测试环境 (test) - 端口 8061                            │
│  └── 生产环境 (prod) - 端口 8062                            │
└─────────────────────────────────────────────────────────────┘
```

## 快速部署

### 1. 环境准备

```bash
# 检查必要工具
node --version    # >= 16.0.0
npm --version     # >= 8.0.0
docker --version  # >= 20.0.0

# 克隆项目
git clone <repository-url>
cd health-assitant-agent

# 给脚本执行权限
chmod +x build.sh wrap.sh
```

### 2. 一键部署

```bash
# 开发环境完整部署
./wrap.sh 1.0.0 dev all

# 测试环境完整部署
./wrap.sh 1.0.0 test all

# 生产环境完整部署
./wrap.sh 1.0.0 prod all
```

## 分步部署

### 步骤1: 构建前端应用

```bash
# 构建指定版本和环境
./build.sh <version> <environment>

# 示例
./build.sh 1.0.0 dev     # 开发环境
./build.sh 1.0.0 test    # 测试环境
./build.sh 1.0.0 prod    # 生产环境
```

构建脚本会执行：
- 依赖安装
- TypeScript类型检查
- 代码格式检查
- 应用构建
- 版本信息生成

### 步骤2: Docker镜像操作

```bash
# 构建Docker镜像
./wrap.sh 1.0.0 dev build

# 推送镜像到仓库
./wrap.sh 1.0.0 dev push

# 部署容器
./wrap.sh 1.0.0 dev deploy
```

### 步骤3: 验证部署

```bash
# 健康检查
curl http://localhost:8060/health

# 版本信息
curl http://localhost:8060/version

# 检查容器状态
docker ps | grep health-assist-agent-frontend
```

## Docker Compose部署

### 单环境部署

```bash
# 设置版本号
export VERSION=1.0.0

# 部署开发环境
docker-compose --profile dev up -d

# 部署测试环境
docker-compose --profile test up -d

# 部署生产环境
docker-compose --profile prod up -d
```

### 多环境同时部署

```bash
# 同时部署所有环境
docker-compose --profile dev --profile test --profile prod up -d

# 查看所有服务状态
docker-compose ps
```

## 手动Docker部署

### 构建镜像

```bash
# 多平台构建 (支持ARM64/AMD64)
docker build \
  --platform linux/amd64 \
  --build-arg VERSION=1.0.0 \
  --build-arg ENVIRONMENT=prod \
  -f Dockerfile \
  -t registry.cn-shanghai.aliyuncs.com/chos/health-assist-agent-frontend-prod:1.0.0 \
  .
```

### 推送镜像

```bash
# 登录阿里云镜像仓库
docker login registry.cn-shanghai.aliyuncs.com

# 推送镜像
docker push registry.cn-shanghai.aliyuncs.com/chos/health-assist-agent-frontend-prod:1.0.0
```

### 运行容器

```bash
# 停止旧容器
docker rm -f health-assist-agent-frontend-prod 2>/dev/null || true

# 启动新容器
docker run \
  --restart always \
  -d \
  -p 8062:80 \
  -e TZ=Asia/Shanghai \
  --name health-assist-agent-frontend-prod \
  registry.cn-shanghai.aliyuncs.com/chos/health-assist-agent-frontend-prod:1.0.0
```

## 环境配置

### 端口分配

| 环境 | 端口 | 容器名称 |
|------|------|----------|
| 开发 | 8060 | health-assist-agent-frontend-dev |
| 测试 | 8061 | health-assist-agent-frontend-test |
| 生产 | 8062 | health-assist-agent-frontend-prod |

### 镜像命名规范

```
registry.cn-shanghai.aliyuncs.com/chos/health-assist-agent-frontend-{environment}:{version}
```

示例：
- `registry.cn-shanghai.aliyuncs.com/chos/health-assist-agent-frontend-dev:1.0.0`
- `registry.cn-shanghai.aliyuncs.com/chos/health-assist-agent-frontend-test:1.0.0`
- `registry.cn-shanghai.aliyuncs.com/chos/health-assist-agent-frontend-prod:1.0.0`

## 监控和维护

### 健康检查

```bash
# 应用健康检查
curl -f http://localhost:8060/health

# 容器健康状态
docker inspect --format='{{.State.Health.Status}}' health-assist-agent-frontend-dev
```

### 日志查看

```bash
# 容器日志
docker logs health-assist-agent-frontend-dev

# Nginx访问日志
docker exec health-assist-agent-frontend-dev tail -f /var/log/nginx/access.log

# Nginx错误日志
docker exec health-assist-agent-frontend-dev tail -f /var/log/nginx/error.log
```

### 容器管理

```bash
# 重启容器
docker restart health-assist-agent-frontend-dev

# 更新容器
./wrap.sh 1.0.1 dev deploy

# 清理旧镜像
docker image prune -f
```

## 故障排除

### 常见问题

1. **构建失败**
   ```bash
   # 清理缓存重试
   rm -rf node_modules package-lock.json
   npm install
   ./build.sh 1.0.0 dev
   ```

2. **Docker构建失败**
   ```bash
   # 清理Docker缓存
   docker system prune -a
   ./wrap.sh 1.0.0 dev build
   ```

3. **容器启动失败**
   ```bash
   # 查看容器日志
   docker logs health-assist-agent-frontend-dev
   
   # 检查端口占用
   lsof -i :8060
   ```

4. **健康检查失败**
   ```bash
   # 检查Nginx配置
   docker exec health-assist-agent-frontend-dev nginx -t
   
   # 重启Nginx
   docker exec health-assist-agent-frontend-dev nginx -s reload
   ```

### 性能优化

1. **镜像大小优化**
   - 使用多阶段构建
   - 清理不必要文件
   - 使用.dockerignore

2. **启动速度优化**
   - 预热镜像
   - 优化依赖安装
   - 使用镜像缓存

3. **运行时优化**
   - 配置合适的资源限制
   - 启用Gzip压缩
   - 配置静态资源缓存

## 安全建议

1. **镜像安全**
   - 定期更新基础镜像
   - 扫描安全漏洞
   - 使用非root用户运行

2. **网络安全**
   - 配置防火墙规则
   - 使用HTTPS
   - 限制容器网络访问

3. **数据安全**
   - 定期备份重要数据
   - 加密敏感配置
   - 审计访问日志
