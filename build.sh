#!/bin/bash

# 健康助理前端构建脚本
# 使用方法: ./build.sh [version] [environment]
# 示例: ./build.sh 1.0.0 dev

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -lt 1 ]; then
    log_error "缺少版本号参数"
    echo "使用方法: $0 <version> [environment]"
    echo "示例: $0 1.0.0 dev"
    exit 1
fi

VERSION=$1
ENVIRONMENT=${2:-dev}  # 默认为dev环境

# 验证版本号格式 (语义化版本)
if ! [[ $VERSION =~ ^[0-9]+\.[0-9]+\.[0-9]+(-[a-zA-Z0-9]+)?$ ]]; then
    log_error "版本号格式不正确，请使用语义化版本格式 (如: 1.0.0 或 1.0.0-beta)"
    exit 1
fi

# 验证环境参数
if [[ ! "$ENVIRONMENT" =~ ^(dev|test|prod)$ ]]; then
    log_error "环境参数不正确，支持的环境: dev, test, prod"
    exit 1
fi

log_info "开始构建健康助理前端应用"
log_info "版本: $VERSION"
log_info "环境: $ENVIRONMENT"

# 检查必要工具
check_tool() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 未安装，请先安装"
        exit 1
    fi
}

log_info "检查构建工具..."
check_tool "node"
check_tool "npm"

# 显示Node.js版本
NODE_VERSION=$(node --version)
NPM_VERSION=$(npm --version)
log_info "Node.js版本: $NODE_VERSION"
log_info "NPM版本: $NPM_VERSION"

# 检查Node.js版本 (需要16+)
NODE_MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
if [ "$NODE_MAJOR_VERSION" -lt 16 ]; then
    log_error "Node.js版本过低，需要16.0.0或更高版本"
    exit 1
fi

# 清理之前的构建产物
log_info "清理之前的构建产物..."
rm -rf dist/
rm -rf node_modules/.cache/

# 设置环境变量
export NODE_ENV=production
export GENERATE_SOURCEMAP=false

# 根据环境设置不同的构建配置
case $ENVIRONMENT in
    "dev")
        export VITE_APP_ENV=development
        ;;
    "test")
        export VITE_APP_ENV=testing
        ;;
    "prod")
        export VITE_APP_ENV=production
        ;;
esac

log_info "设置环境变量完成"

# 安装依赖
log_info "安装项目依赖..."
npm ci --silent

# 跳过类型检查和格式检查，直接构建
log_info "跳过类型检查和格式检查，直接进行构建..."

# 构建应用
log_info "开始构建应用..."
npm run build

# 检查构建结果
if [ ! -d "dist" ]; then
    log_error "构建失败，dist目录不存在"
    exit 1
fi

if [ ! -f "dist/index.html" ]; then
    log_error "构建失败，index.html文件不存在"
    exit 1
fi

# 显示构建产物大小
log_info "构建产物分析:"
du -sh dist/
find dist/ -name "*.js" -o -name "*.css" | xargs ls -lh

# 创建版本信息文件
cat > dist/version.json << EOF
{
  "version": "$VERSION",
  "environment": "$ENVIRONMENT",
  "buildTime": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "gitCommit": "$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')",
  "gitBranch": "$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo 'unknown')"
}
EOF

log_success "构建完成!"
log_info "版本: $VERSION"
log_info "环境: $ENVIRONMENT"
log_info "构建时间: $(date)"
log_info "构建产物位置: ./dist/"

# 可选：压缩构建产物
if command -v tar &> /dev/null; then
    ARCHIVE_NAME="health-assistant-frontend-${VERSION}-${ENVIRONMENT}.tar.gz"
    log_info "创建构建产物压缩包: $ARCHIVE_NAME"
    tar -czf "$ARCHIVE_NAME" -C dist .
    log_success "压缩包创建完成: $ARCHIVE_NAME"
fi

log_success "构建脚本执行完成!"
