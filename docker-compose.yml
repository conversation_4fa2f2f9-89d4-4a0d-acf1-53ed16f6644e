# Docker Compose 配置文件 - 健康助理前端多环境部署
version: '3.8'

services:
  # 开发环境
  health-assistant-dev:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        VERSION: ${VERSION:-1.0.0}
        ENVIRONMENT: dev
      platforms:
        - linux/amd64
    image: registry.cn-shanghai.aliyuncs.com/chos/health-assist-agent-frontend-dev:${VERSION:-1.0.0}
    container_name: health-assist-agent-frontend-dev
    restart: unless-stopped
    ports:
      - "8060:80"
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - dev-logs:/var/log/nginx
    networks:
      - health-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    labels:
      - "com.docker.compose.project=health-assistant"
      - "com.docker.compose.service=frontend-dev"
      - "com.docker.compose.environment=dev"
    profiles:
      - dev

  # 测试环境
  health-assistant-test:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        VERSION: ${VERSION:-1.0.0}
        ENVIRONMENT: test
      platforms:
        - linux/amd64
    image: registry.cn-shanghai.aliyuncs.com/chos/health-assist-agent-frontend-test:${VERSION:-1.0.0}
    container_name: health-assist-agent-frontend-test
    restart: unless-stopped
    ports:
      - "8061:80"
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - test-logs:/var/log/nginx
    networks:
      - health-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    labels:
      - "com.docker.compose.project=health-assistant"
      - "com.docker.compose.service=frontend-test"
      - "com.docker.compose.environment=test"
    profiles:
      - test

  # 生产环境
  health-assistant-prod:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        VERSION: ${VERSION:-1.0.0}
        ENVIRONMENT: prod
      platforms:
        - linux/amd64
    image: registry.cn-shanghai.aliyuncs.com/chos/health-assist-agent-frontend-prod:${VERSION:-1.0.0}
    container_name: health-assist-agent-frontend-prod
    restart: unless-stopped
    ports:
      - "8062:80"
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - prod-logs:/var/log/nginx
    networks:
      - health-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    labels:
      - "com.docker.compose.project=health-assistant"
      - "com.docker.compose.service=frontend-prod"
      - "com.docker.compose.environment=prod"
    profiles:
      - prod

# 数据卷
volumes:
  dev-logs:
    driver: local
  test-logs:
    driver: local
  prod-logs:
    driver: local

# 网络配置
networks:
  health-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16