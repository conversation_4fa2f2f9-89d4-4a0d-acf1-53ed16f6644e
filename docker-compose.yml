# Docker Compose 配置文件
version: '3.8'

services:
  # 健康助理前端应用
  health-assistant:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
      platforms:
        - linux/amd64
        - linux/arm64  # 支持M1芯片
    container_name: health-assistant-app
    restart: unless-stopped
    ports:
      - "3000:80"
    environment:
      - TZ=Asia/Shanghai
    volumes:
      # 如果需要自定义nginx配置
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      # 日志持久化
      - logs:/var/log/nginx
    networks:
      - health-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "com.docker.compose.project=health-assistant"
      - "com.docker.compose.service=frontend"

# 数据卷
volumes:
  logs:
    driver: local
  proxy-logs:
    driver: local

# 网络配置
networks:
  health-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16