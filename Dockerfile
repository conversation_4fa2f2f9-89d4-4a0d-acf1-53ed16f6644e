# 多阶段构建 Dockerfile - 健康助理前端
# 支持多平台构建 (linux/amd64, linux/arm64)

# 构建参数
ARG VERSION=latest
ARG ENVIRONMENT=production

# 阶段1: 构建阶段
FROM --platform=$BUILDPLATFORM node:18-alpine AS builder

# 设置构建参数
ARG VERSION
ARG ENVIRONMENT

# 设置工作目录
WORKDIR /app

# 安装构建工具
RUN apk add --no-cache git

# 设置npm镜像源和配置
RUN npm config set registry https://registry.npmmirror.com && \
    npm config set fetch-retry-mintimeout 60000 && \
    npm config set fetch-retry-maxtimeout 300000

# 复制package文件
COPY package*.json ./

# 安装依赖 (包含devDependencies用于构建)
RUN npm ci --silent

# 复制源代码
COPY . .

# 设置构建环境变量
ENV NODE_ENV=production
ENV GENERATE_SOURCEMAP=false
ENV VITE_APP_VERSION=$VERSION
ENV VITE_APP_ENV=$ENVIRONMENT

# 构建应用
RUN npm run build && \
    echo "构建完成，产物大小:" && \
    du -sh dist/

# 阶段2: 生产阶段
FROM nginx:1.25-alpine AS production

# 设置标签信息
LABEL maintainer="Health Assistant Team"
LABEL version=$VERSION
LABEL environment=$ENVIRONMENT
LABEL description="健康助理前端应用"

# 安装必要工具和安全更新
RUN apk add --no-cache \
    tzdata \
    curl \
    wget \
    && apk upgrade --no-cache

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建nginx用户 (如果不存在)
RUN if ! getent group nginx > /dev/null 2>&1; then \
        addgroup -g 101 -S nginx; \
    fi && \
    if ! getent passwd nginx > /dev/null 2>&1; then \
        adduser -S -D -H -u 101 -h /var/cache/nginx -s /sbin/nologin -G nginx -g nginx nginx; \
    fi

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 创建必要目录并设置权限
RUN mkdir -p /var/cache/nginx/client_temp \
    && mkdir -p /var/cache/nginx/proxy_temp \
    && mkdir -p /var/cache/nginx/fastcgi_temp \
    && mkdir -p /var/cache/nginx/uwsgi_temp \
    && mkdir -p /var/cache/nginx/scgi_temp \
    && mkdir -p /var/log/nginx \
    && chown -R nginx:nginx /var/cache/nginx \
    && chown -R nginx:nginx /var/log/nginx \
    && chown -R nginx:nginx /etc/nginx/conf.d \
    && chown -R nginx:nginx /usr/share/nginx/html

# 健康检查 - 使用多种方式确保可靠性
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:80/health || wget --no-verbose --tries=1 --spider http://localhost:80/health || exit 1

# 暴露端口
EXPOSE 80

# 添加版本信息到容器
RUN echo "VERSION=$VERSION" > /etc/container-info && \
    echo "ENVIRONMENT=$ENVIRONMENT" >> /etc/container-info && \
    echo "BUILD_DATE=$(date -u +%Y-%m-%dT%H:%M:%SZ)" >> /etc/container-info

# 使用非root用户运行
USER nginx

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]