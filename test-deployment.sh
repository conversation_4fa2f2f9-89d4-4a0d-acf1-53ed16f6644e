#!/bin/bash

# 健康助理前端部署测试脚本

set -e

echo "=== 健康助理前端部署测试 ==="

# 检查容器是否运行
echo "1. 检查容器状态..."
if docker ps | grep -q health-assit-agent-frontend-dev; then
    echo "✅ 容器正在运行"
    docker ps | grep health-assit-agent-frontend-dev
else
    echo "❌ 容器未运行"
    exit 1
fi

# 检查端口是否可访问
echo "2. 检查端口访问..."
if curl -f -s http://localhost:8060 > /dev/null; then
    echo "✅ 端口8060可访问"
else
    echo "❌ 端口8060不可访问"
    exit 1
fi

# 检查应用响应
echo "3. 检查应用响应..."
RESPONSE=$(curl -s http://localhost:8060)
if echo "$RESPONSE" | grep -q "健康助理"; then
    echo "✅ 应用正常响应"
else
    echo "❌ 应用响应异常"
    echo "响应内容: $RESPONSE"
    exit 1
fi

# 检查静态资源
echo "4. 检查静态资源..."
if curl -f -s http://localhost:8060/assets/ > /dev/null 2>&1; then
    echo "✅ 静态资源可访问"
else
    echo "⚠️  静态资源检查跳过"
fi

# 显示容器日志
echo "5. 容器日志 (最后10行):"
docker logs --tail 10 health-assit-agent-frontend-dev

echo ""
echo "=== 测试完成 ==="
echo "✅ 健康助理前端部署测试通过"
echo "🌐 访问地址: http://localhost:8060"
